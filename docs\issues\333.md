---
title: 设置项功能介绍
---

# 设置项功能介绍

- XIAOMUSIC_ACTIVE_CMD 环境变量，对应后台的 【允许唤醒的命令】，用于唤醒口令，配置成'play,random_play'，在非播放状态下，只有这两个指令（播放歌曲和随机播放）可以触发，触发后，xiaomusic进入playing状态，其他指令则可以正常触发。具体见 <https://github.com/hanxi/xiaomusic/pull/43>
- XIAOMUSIC_EXCLUDE_DIRS 配置歌曲目录里需要忽略的目录，对应后台的 【忽略目录】
- XIAOMUSIC_MUSIC_PATH_DEPTH 配置歌曲目录搜索深度，对应后台的 【目录深度】，具体见 </issues/76.html>
- XIAOMUSIC_DISABLE_HTTPAUTH 配置成 false 表示开启密码访问web控制台，对应后台的 【关闭密码验证】，具体见 </issues/47.html>
- XIAOMUSIC_HTTPAUTH_USERNAME 配置 web 控制台用户，对应后台的 【控制台账户】
- XIAOMUSIC_HTTPAUTH_PASSWORD 配置 web 控制台密码，对应后台的 【控制台密码】
- XIAOMUSIC_CONF_PATH 用来存放配置文件的目录，对应后台的 【配置文件目录】，记得把目录映射到主机，默认为 `/app/config` ，具体见 </issues/74.html>
- XIAOMUSIC_CACHE_DIR 用来音乐 tag 缓存，默认为 `/app/cache`，对应后台的 【缓存文件目录】。
- XIAOMUSIC_DISABLE_DOWNLOAD 设为 true 时关闭下载功能，对应后台的 【关闭下载功能】，见 </issues/82.html>
- XIAOMUSIC_USE_MUSIC_API 设为 true 时使用 player_play_music 接口播放音乐，对应后台的 【型号兼容模式】，用于兼容不能播放的型号，如果发现需要设置这个选项的时候请告知我加一下设备型号，方便以后不用设置。 见 </issues/30.html>
- XIAOMUSIC_KEYWORDS_PLAY 用来播放歌曲的口令前缀，对应后台的 【播放歌曲口令】，默认是 "播放歌曲,放歌曲" ，可以用英文逗号分割配置多个
- XIAOMUSIC_KEYWORDS_STOP 用来关机的口令，对应后台的 【停止口令】，默认是 "关机,暂停,停止" ，可以用英文逗号分割配置多个。
- XIAOMUSIC_KEYWORDS_PLAYLOCAL 用来播放本地歌曲的口令前缀，对应后台的 【播放本地歌曲口令】，本地找不到时不会下载歌曲，默认是 "播放本地歌曲,本地播放歌曲" ，可以用英文逗号分割配置多个。
- XIAOMUSIC_ENABLE_FUZZY_MATCH 设为 true 时开启模糊匹配（默认），设为 false 时关闭模糊匹配，对应后台的 【开启模糊搜索】，支持模糊匹配歌名和歌单名。 具体见 </issues/52.html>
- XIAOMUSIC_FUZZY_MATCH_CUTOFF 设置模糊搜索匹配的最低相似度阈值（默认0.6，可以配0到1直接的小数），越小越模糊，越大越精准，对应后台的 【模糊匹配阈值】。具体见 </issues/52.html>
- XIAOMUSIC_PUBLIC_PORT 用于设置外网端口，对应后台的 【外网访问端口】，当使用反向代理时可以设置为外网端口，XIAOMUSIC_HOSTNAME 设为外网IP或者域名即可。
- XIAOMUSIC_DOWNLOAD_PATH 变量可以配置下载目录，默认为空，表示使用 music 目录为下载目录，对应后台的 【音乐下载目录】。设置这个目录必须是 music 的子目录，否则刷新列表后会找不到歌曲。具体见 </issues/98.html>
- XIAOMUSIC_PROXY 用于配置国内使用 youtube 源下载歌曲时使用的代理，参数格式参考 yt-dlp 文档说明。 见 </issues/2.html> 和 </issues/11.html>
- MIIO_TTS_CMD 用于部分机型（如：`L05C`）使用 MiIO 支持 tts 能力，默认为空，命令选择见 [MiService-fork 文档](https://github.com/yihong0618/MiService)


## 评论

没有评论。
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/333)
