[project]
name = "xiaomusic"
version = "0.3.83"
description = "Play Music with xiaomi AI speaker"
authors = [
{name = "涵曦", email = "<EMAIL>"},
]
dependencies = [
    "aiohttp>=3.8.6",
    "miservice-fork>=2.7.0",
    "watchdog>=6.0.0",
    "mutagen>=1.47.0",
    "yt-dlp[default]>=2024.12.1.232904.dev0",
    "uvicorn>=0.30.1",
    "fastapi>=0.115.4",
    "starlette>=0.37.2",
    "aiofiles>=24.1.0",
    "ga4mp>=2.0.4",
    "apscheduler>=3.10.4",
    "opencc-python-reimplemented==0.1.7",
    "pillow>=10.4.0",
    "python-multipart>=0.0.12",
    "requests>=2.32.3",
    "sentry-sdk[fastapi]==1.45.1",
    "python-socketio>=5.12.1",
    "openai>=1.93.3",
]
requires-python = ">=3.10"
readme = "README.md"
license = {text = "MIT"}

[project.urls]
Homepage = "https://github.com/hanxi/xiaomusic"

[project.scripts]
xiaomusic = "xiaomusic.cli:main"

[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"

[tool.pdm]
[tool.pdm.dev-dependencies]
lint = [
    "ruff>=0.4.8",
]
dev = [
    "commitizen>=3.27.0",
]
[tool.ruff]
lint.select = [
    "B", # flake8-bugbear
    "C4", # flake8-comprehensions
    "E", # pycodestyle - Error
    "F", # Pyflakes
    "I", # isort
    "W", # pycodestyle - Warning
    "UP", # pyupgrade
]
lint.ignore = [
    "E501", # line-too-long
    "W191", # tab-indentation
]
include = ["**/*.py", "**/*.pyi", "**/pyproject.toml"]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.flake8-bugbear]
extend-immutable-calls = [
    "fastapi.Depends",
    "fastapi.params.Depends",
    "fastapi.Query",
    "fastapi.params.Query",
    "fastapi.File"
]

[tool.pdm.scripts]
lint = "ruff check ."
fmt = "ruff format ."
lintfmt = {composite = ["ruff check --fix .", "ruff format ."]}

[tool.commitizen]
name = "cz_conventional_commits"
tag_format = "v$version"
version_scheme = "pep440"
version_provider = "pep621"
update_changelog_on_bump = true
major_version_zero = true
version_files = [
    "xiaomusic/__init__.py",
]
