import asyncio
from abc import ABC, abstractmethod

class Plugin(ABC):
    def __init__(self) -> None:
        super().__init__()

    @property
    def log(self):
        global log
        return log

    @abstractmethod
    async def check_full_match_cmd(self, did: str, cmd: str, ctrl_panel: bool = False) -> bool:
        return False

    @abstractmethod
    async def check_pattern_match_cmd(self, did: str, cmd: str, ctrl_panel: bool = False) -> bool:
        return False

    async def do_tts(self, did: str, content: str):
        global xiaomusic
        await xiaomusic.do_tts(did, content)
