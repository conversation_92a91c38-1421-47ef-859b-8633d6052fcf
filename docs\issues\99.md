---
title: 💬 FAQ问题集合
---

# 💬 FAQ问题集合

> [!NOTE]
> 这个 issue 用来总结报错日志和对应的解决方法。

## ❓  XIAOMUSIC_HOSTNAME 怎么填

填写 docker 主机的 ip ，**不是小爱音箱的ip**，一般就是网页访问的后台地址里的 ip ，只要确保 ip 和小爱音箱在一个局域网内就行。

同时也支持 xx.xx.com 的域名格式，用于配置反代供外网访问，比如小爱音箱和 docker 主机不在同一个局域网内。


## ❓  Login Failed 登陆失败

表现就是 **后台看不到设备列表** ，日志中会有对应的报错。

这个有专门的讨论，见 </issues/16.html> ，一般是因为开了加速代理，关代理再尝试即可。也可以试试在局域网设备里的米家app上退出再重新登录一下。

在小米官网 www.mi.com 登陆过人脸或滑块验证基本上能解决 99%的 login failed 问题。

来自 @yilikun 的友情提示：
> 1. 关闭本地代理。
> 2. 如果是nas运行的，网络由bridge改为host。
> 3. 米家app重新登陆。
> 4. mi.com官网重新登陆。
> 5. 检查 setting.json 文件里的账号密码是否正确。

如果是在 openwrt 类路由器系统上安装的，请检查路由器的防火墙设置。

## ❓  网页后台可以播放，语音控制无效

这种情况是拉取不到对话记录导致的。
如果是首次在网页后台保存 did 后需要重启一次容器。
其他情况可能是被限制拉取对话记录次数，也可以尝试重启容器。
还有一种情况是配错了唤醒口令，可以在小爱音箱app里查看对话记录，也可以查看 xiaomusic 的日志。默认口令前缀是【播放歌曲】，没有这个前缀是无法识别的，说播放音乐是没用的，除非自己设置其他口令词。
已知 `M01/XMYX01JY 小米小爱音箱HD` 获取对话记录的接口比较特殊，需要开启【特殊型号获取对话记录:】开关才能正常语音控制。

## ❓  日志显示正在播放，却没有声音

可以点击播放链接按钮，看看默认的那个链接能否播放。

已知部分触屏版不能播放可以在后台设置 【型号兼容模式】为 true 试试。如果你的设备型号需要手动设置这个选项，请在 /issues/453.html 中反馈。

其他情况可能是 XIAOMUSIC_HOSTNAME 配错了地址，不是 docker 主机地址会导致小爱音箱无法访问到，而且需要和小爱音箱在同一个**局域网**下的地址。还有可能是端口配错了，**修改了默认 8090 端口映射**，需要同步修改其他参数，可以翻阅端口修改的文档。

如果端口不是8090，首次启动没配好端口的话，需要手动修改setting.json文件里的端口，或者把setting.json文件删除重新配置，或者在后台修改监听端口后重启。

可以点击播放歌曲后，查看日志里的歌曲链接，放到浏览器里打开试试，不能访问说明是端口或者hostname问题，如果是异地访问，需要把 hostname 修改为外网ip或者域名，需要注意音箱只支持访问ipv4，不能是ipv6的公网。

如果是配了公网反代端口，注意区分是 http 还是 https ，如果是 https 的，配置 XIAOMUSIC_HOSTNAME 时需要加上 `https:// ` 前缀。

如果是在 openwrt 类路由器系统上安装的，请检查路由器的防火墙设置。

如果是在 windows 上安装的，请关闭防火墙。

## ❓  无法播放 flac 格式歌曲

因设备差异和文件格式差异，已知部分设备不支持 flac 格式，比如 L05B L05C 。

## ❓  docker 镜像拉取失败

请更换镜像源或者使用代理。不同环境更换镜像源的方式不一样，可以网上搜索自己的 NAS 如何更换镜像源。

已经可以通过 [DaoCloud](https://github.com/DaoCloud/public-image-mirror) 拉取镜像。

```
docker pull m.daocloud.io/docker.io/hanxi/xiaomusic:latest
docker tag m.daocloud.io/docker.io/hanxi/xiaomusic:latest hanxi/xiaomusic:latest
```

更多镜像源见 </issues/398.html>

## ❓  启动失败，日志中出现 RuntimeError: can't start new thread

一般是 docker 版本太低，或者系统限制了 docker 使用的 cpu 数量，可以尝试升级 docker 到最新版本。

## ❓  DNS 解析错误

一般会出现下面这样的日志，表现就是设置页面看不到设备列表。

```
aiohttp.client_ _exceptions. ClientConnectorError: Cannot connect to host account.xiaomi.com:443 ssl:False [Temporary failure in name resolution]
```

可以尝试把主机的 DNS 设为 ********* 之后重启 docker 主机。

如果还是不行可以把 docker 的网络模式改成 host 模式。


## ❓  点击播放后需要很久才开始播放的问题

这个问题新版本已经解决，如果还存在请反馈。

~目前0.3.x版本还存在这个问题没有完全解决，可以暂时回退到0.2.0版本继续使用。~

## ❓  如何配置多个歌曲目录

```yaml
services:
  xiaomusic:
    image: hanxi/xiaomusic
    container_name: xiaomusic
    restart: unless-stopped
    ports:
      - 8090:8090
    volumes:
      - /data/music1:/app/music/music1
      - /data/music2:/app/music/music2
      - /data/download:/app/music/download
      - /data/xiaomusic/conf:/app/conf
```

冒号左边的 `/data/music1` 和 `/data/music2` 改成你的目录即可。如果你是 windows 的 docker ，可以改成 `D:/music1` 和 `D:/music2`，盘符号开头，用 `/` 分割。

如果是 docker 部署的，建议不要去修改 web 后台里的音乐路径和配置路径等等所有路径除非你熟悉 docker 的目录映射机制。

## ❓  能不能中文名

```yaml
services:
  xiaomusic:
    image: hanxi/xiaomusic
    container_name: xiaomusic
    restart: unless-stopped
    ports:
      - 8090:8090
    volumes:
      - /data/music1:/app/music/歌曲目录1
      - /data/music2:/app/music/歌曲目录2
      - /data/download:/app/music/download
      - /data/xiaomusic/conf:/app/conf
```

## ❓  能不能多层目录

可以，每层的每个目录会识别为一个播放列表。

##  ❓  是否需要手动获取 did

新版本不需要手动获取配置 did，不需要配置环境变量，直接在 web 后台填入小米账号密码保存后会自动获取到 did ，然后勾选对应的设备即可。

## ❓  报错 601

报错日志大致如下:

```txt
Exception: Error https://api2.mina.mi.com/remote/ubus: {"code":601,"message":"illegal argument exception","data":"IllegalArgumentException: ubus call format illegal!"}
```

原因是没有配置 did ，或者 did 配置错误。可以到设置页面选择正确的设备类型和 did 然后保存。

## ❓ 新功能没有生效

在设置页面重新保存一下，或者删除 setting.json 文件，重新在后台设置一次。

## ❓ 为什么会先说小爱音箱自带的回答，再说下载中或者过一会儿才播放本地歌曲

设计原理就是每秒不停的抓取对话记录，然后再打断小爱音箱自带的处理流程。整个过程下来会有延时，所以打断不会很及时，做不到无缝衔接。

## ❓ 云服务器上能否安装

可以安装，登录 mi.com 这个步骤可以使用终端里的浏览器 [carbonyl](https://github.com/fathyb/carbonyl) 来操作：

```shell
docker run --rm -ti fathyb/carbonyl https://mi.com
```

或者使用 [browsh](https://github.com/browsh-org/browsh)

```shell
docker run --rm -it browsh/browsh --startup-url https://mi.com
```

## 在播放歌曲时，问小爱查询天气，小爱查询天气时，中途会断掉。

播放中会被xiaomusic接管，会跟原有功能冲突，需要先说关机来关闭xiaomusic的功能，再问小爱查询天气。

## 我家有个奇怪的问题，你给它暂停了，它过一会儿又自己开始唱了，不勘其扰。小爱重启都没用。

要说【关机】才能关掉 xiaomusic ，否则下一曲定时器到了会继续播放下一曲。

## 为什么播放进度条不能拖动

没有接口，目前做不到。

## 小爱触屏能不能显示本地的歌名歌词封面

可以，设置页面打开【启用继续播放】开关。

## 看不到歌曲列表

1. 检查目录挂载是否正确
2. 检查目录权限是否正确
3. 检查文件夹名字是否是 UTF-8 格式


## 本机有声音，小爱音箱没声音，请问什么原因？

排查步骤:

1. 测试链接是否能播放，能播放说明小爱音箱控制没问题，继续下一步排查。不能播放说明控制不了你的小爱音箱，一般是【型号兼容模式】设置问题。
3. 小爱音箱和 NAS 是否在同一个局域网，检查 NAS 的 IP 和音箱的 IP 是否在同一个网段，不在同一个网段会导致音箱无法访问到 NAS 。如果在同一个局域网，检查 NAS 上的防火墙配置，关闭防火墙再测试，如果还是不行就继续下一步。
4. 查看容器日志中的歌曲链接是否正常，点击后台页面上的播放歌曲时，容器中会有歌曲链接，一般是 http 开头的链接，复制完整链接到浏览器试试看能否打开，能打开说明网络没问题，继续下一步排查。不能打开有可能是 ip 和端口配置错误，请使用设置页面的自动填按钮自动填 ip 和端口。
5. 歌曲文件格式是否是 mp3 格式，有些型号无法播放 flac 格式的歌曲，请使用 mp3 格式的歌曲文件测试，一个不行就多找几个文件测试。

## 在安装好第一次用语音正常，第二天就不能用语音了

拉取对话记录的问题。拉取对话记录太频繁了，可以使用[定时任务功能](/issues/182.html)，设置晚上关闭拉取对话记录。比如这样设置是早上6点开启拉取对话记录，晚上12点关闭：
```
[
   {
        "expression": "0 6 * * *",
        "name": "set_pull_ask",
        "arg1": "enable"
   },
   {
        "expression": "0 0 * * *",
        "name": "set_pull_ask",
        "arg1": "disable"
   }
]
```

## 播放下一首歌曲时会重复播放上一首歌曲的前几秒

时间延迟问题，可以把【下一首歌延迟播放秒数】设置成负数，表示提前几秒结束播放。


## 评论


### 评论 1 - shissx

安装的最新版本，即使没有使用，日志一直在不停的刷新，示例：
[10:20:36] [0.1.101] [DEBUG] Polling_event, timestamp: {'eeb70da5-baa9-4b56-b2f3-7ee01276a18a': 1720430457236}
[10:20:36] [0.1.101] [DEBUG] Sleep 0.0003166699898429215, timestamp: {'eeb70da5-baa9-4b56-b2f3-7ee01276a18a': 1720430457236}
[10:20:37] [0.1.101] [DEBUG] Listening new message, timestamp: {'eeb70da5-baa9-4b56-b2f3-7ee01276a18a

之前的版本没有这个问题，这个是设置错误？还是本来就如此呢？

---

### 评论 2 - hanxi

> 安装的最新版本，即使没有使用，日志一直在不停的刷新，示例： [10:20:36] [0.1.101] [DEBUG] Polling_event, timestamp: {'eeb70da5-baa9-4b56-b2f3-7ee01276a18a': 1720430457236} [10:20:36] [0.1.101] [DEBUG] Sleep 0.0003166699898429215, timestamp: {'eeb70da5-baa9-4b56-b2f3-7ee01276a18a': 1720430457236} [10:20:37] [0.1.101] [DEBUG] Listening new message, timestamp: {'eeb70da5-baa9-4b56-b2f3-7ee01276a18a
> 
> 之前的版本没有这个问题，这个是设置错误？还是本来就如此呢？

正常现象，现在默认把调试日志打开了，可以在后台设置关闭调试日志的。

---

### 评论 3 - Dx0123

大佬，docker安装提示缺少很多module，我一个个在dockerfilie里加上，最后卡在miservice装不上了~

---

### 评论 4 - hanxi

@Dx0123 其实不用手动安装依赖的，直接一行应该就行。
```
RUN pip install -U xiaomusic
```



---

### 评论 5 - Dx0123

> @Dx0123 其实不用手动安装依赖的，直接一行应该就行。
> 
> ```
> RUN pip install -U xiaomusic
> ```

我直接用pip安装好之后，执行仍然有缺少的依赖，和docker里缺的一样。截图的module安装了之后还会有其他依赖缺失
![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/9fb182e2-ad41-488d-8633-4bae06202f73)


---

### 评论 6 - hanxi

@Dx0123 你的python版本是不是有问题？你参考下项目里的Dockerfile，用同一个From镜像试试。

---

### 评论 7 - hanxi

> 有时候指令已停止，可是一会儿，又自动播放下一首，根本就停不下来。需要重启容器才能正常

点关机按钮也不行吗？

---

### 评论 8 - hanxi

> > > 有时候指令已停止，可是一会儿，又自动播放下一首，根本就停不下来。需要重启容器才能正常
> > 
> > 
> > 点关机按钮也不行吗？
> 
> 是的，说关机，点关机，暂停都不行。只能重启容器。

有没有日志看看？

---

### 评论 9 - sqmcool

为什么我的没有显示设备？
![Snipaste_2024-09-14_15-51-00](https://gproxy.hanxi.cc/proxy/user-attachments/assets/245d9a85-4ee1-4c82-be0d-865a29827072)


---

### 评论 10 - hanxi

> 为什么我的没有显示设备？ ![Snipaste_2024-09-14_15-51-00](https://private-user-images.githubusercontent.com/14977818/367498420-245d9a85-4ee1-4c82-be0d-865a29827072.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjYzMDQ4NjksIm5iZiI6MTcyNjMwNDU2OSwicGF0aCI6Ii8xNDk3NzgxOC8zNjc0OTg0MjAtMjQ1ZDlhODUtNGVlMS00YzgyLWJlMGQtODY1YTI5ODI3MDcyLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDA5MTQlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwOTE0VDA5MDI0OVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPThjNzJkYTQyYzVhMmU2N2Y3M2MwZjc4ZjE5MDg4ODFlMGVhYjdmZGYxMGFjYmI2N2ViN2JlOGUzZTEwYWMxZmUmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0JmFjdG9yX2lkPTAma2V5X2lkPTAmcmVwb19pZD0wIn0.4EaUyKhUmE1KNVseE_b25oJGnWSeKDsKpjzViAeltHY)

应该是登陆失败，可以查看一下日志。

---

### 评论 11 - schppd

楼主您好，请问这个我需要怎么处理？
![微信截图_20240915225040](https://gproxy.hanxi.cc/proxy/user-attachments/assets/d4ecb7fb-6a47-4c66-bbab-72babf4afb9c)


---

### 评论 12 - hanxi

> 楼主您好，请问这个我需要怎么处理？ ![微信截图_20240915225040](https://private-user-images.githubusercontent.com/108209415/367590244-d4ecb7fb-6a47-4c66-bbab-72babf4afb9c.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjY0MTI0NjcsIm5iZiI6MTcyNjQxMjE2NywicGF0aCI6Ii8xMDgyMDk0MTUvMzY3NTkwMjQ0LWQ0ZWNiN2ZiLTZhNDctNGM2Ni1iYmFiLTcyYmFiZjRhZmI5Yy5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjQwOTE1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI0MDkxNVQxNDU2MDdaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT02Yjg5ZTU4MDAzNDc5YmI2OTdlM2MxYjM0MGIwY2U4YmExOTk2MmIyMTM0MGIxNmFjYmZmMDIxYzc1Mjk5YTE5JlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCZhY3Rvcl9pZD0wJmtleV9pZD0wJnJlcG9faWQ9MCJ9.zTAh3m7_AxAIS00LTRHrX23zDD1tVWuXD6wDPsiia9g)

删掉重新配置一下试试。

---

### 评论 13 - schppd

会不会跟网络不稳定有关系？我都弄了几次还是这样子




------------------&nbsp;原始邮件&nbsp;------------------
发件人:                                                                                                                        "hanxi/xiaomusic"                                                                                    ***@***.***&gt;;
发送时间:&nbsp;2024年9月15日(星期天) 晚上10:57
***@***.***&gt;;
***@***.******@***.***&gt;;
主题:&nbsp;Re: [hanxi/xiaomusic] FAQ问题集合 (Issue #99)





  
楼主您好，请问这个我需要怎么处理？ 
  
删掉重新配置一下试试。
 
—
Reply to this email directly, view it on GitHub, or unsubscribe.
You are receiving this because you commented.Message ID: ***@***.***&gt;

---

### 评论 14 - hanxi

> 会不会跟网络不稳定有关系？我都弄了几次还是这样子
> […](#)
> ------------------&nbsp;原始邮件&nbsp;------------------ 发件人: "hanxi/xiaomusic" ***@***.***&gt;; 发送时间:&nbsp;2024年9月15日(星期天) 晚上10:57 ***@***.***&gt;; ***@***.******@***.***&gt;; 主题:&nbsp;Re: [hanxi/xiaomusic] FAQ问题集合 (Issue #99) 楼主您好，请问这个我需要怎么处理？ 删掉重新配置一下试试。 — Reply to this email directly, view it on GitHub, or unsubscribe. You are receiving this because you commented.Message ID: ***@***.***&gt;

有可能的，用代理试试。

---

### 评论 15 - guoxiangke

定制的时候，"全部"和”所有歌曲"的区别，帮助有需要的朋友：
歌单中 "全部" 指的是 所有歌单中歌曲，但不包括“歌单内容”配置（http://127.0.0.1:8090/static/setting.html）中的电台 "type": "radio",的
”所有歌曲" 指的是下载的歌曲，在download文件夹里



---

### 评论 16 - agigogo

![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/4fed0748-5497-47b5-ac9c-9cbc1d826a94)
在docker里可以运行，但是没法播放设置页面中的播放链接，选中设备那里是空的，是不是没成功？怎么调整？


---

### 评论 17 - hanxi

> ![image](https://private-user-images.githubusercontent.com/73272860/369268116-4fed0748-5497-47b5-ac9c-9cbc1d826a94.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjY4MTQ0MzksIm5iZiI6MTcyNjgxNDEzOSwicGF0aCI6Ii83MzI3Mjg2MC8zNjkyNjgxMTYtNGZlZDA3NDgtNTQ5Ny00N2I1LWFjOWMtOWNiYzFkODI2YTk0LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDA5MjAlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwOTIwVDA2MzUzOVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWFkNzg5MDQ1ZDQ5N2VmODg5MWQzYzM4MWY5OTI1ZDRhNzQ3ZjNjM2VmOTA1MDQwNGU3ZGM0Y2I1MDkwY2MzNWUmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.GlUL5Oqk9-SqLH9chSQxxm9sI3tkbqNoQ8To8UjrawE) 在docker里可以运行，但是没法播放设置页面中的播放链接，选中设备那里是空的，是不是没成功？怎么调整？

设置页面输入小米的账号密码后，再勾选一个设备。

---

### 评论 18 - agigogo

> > ![image](https://private-user-images.githubusercontent.com/73272860/369268116-4fed0748-5497-47b5-ac9c-9cbc1d826a94.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjY4MTQ0MzksIm5iZiI6MTcyNjgxNDEzOSwicGF0aCI6Ii83MzI3Mjg2MC8zNjkyNjgxMTYtNGZlZDA3NDgtNTQ5Ny00N2I1LWFjOWMtOWNiYzFkODI2YTk0LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDA5MjAlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwOTIwVDA2MzUzOVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWFkNzg5MDQ1ZDQ5N2VmODg5MWQzYzM4MWY5OTI1ZDRhNzQ3ZjNjM2VmOTA1MDQwNGU3ZGM0Y2I1MDkwY2MzNWUmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.GlUL5Oqk9-SqLH9chSQxxm9sI3tkbqNoQ8To8UjrawE) 在docker里可以运行，但是没法播放设置页面中的播放链接，选中设备那里是空的，是不是没成功？怎么调整？
> 
> 设置页面输入小米的账号密码后，再勾选一个设备。

设置页面里没有勾选的选项

> > ![image](https://private-user-images.githubusercontent.com/73272860/369268116-4fed0748-5497-47b5-ac9c-9cbc1d826a94.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjY4MTQ0MzksIm5iZiI6MTcyNjgxNDEzOSwicGF0aCI6Ii83MzI3Mjg2MC8zNjkyNjgxMTYtNGZlZDA3NDgtNTQ5Ny00N2I1LWFjOWMtOWNiYzFkODI2YTk0LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDA5MjAlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwOTIwVDA2MzUzOVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWFkNzg5MDQ1ZDQ5N2VmODg5MWQzYzM4MWY5OTI1ZDRhNzQ3ZjNjM2VmOTA1MDQwNGU3ZGM0Y2I1MDkwY2MzNWUmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.GlUL5Oqk9-SqLH9chSQxxm9sI3tkbqNoQ8To8UjrawE) 在docker里可以运行，但是没法播放设置页面中的播放链接，选中设备那里是空的，是不是没成功？怎么调整？
> 
> 设置页面输入小米的账号密码后，再勾选一个设备。

![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/a4185b88-89b2-4682-b931-47fa39463c0c)
设置页面没有可勾选项？

---

### 评论 19 - agigogo

> > > ![image](https://private-user-images.githubusercontent.com/73272860/369268116-4fed0748-5497-47b5-ac9c-9cbc1d826a94.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjY4MTQ0MzksIm5iZiI6MTcyNjgxNDEzOSwicGF0aCI6Ii83MzI3Mjg2MC8zNjkyNjgxMTYtNGZlZDA3NDgtNTQ5Ny00N2I1LWFjOWMtOWNiYzFkODI2YTk0LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDA5MjAlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwOTIwVDA2MzUzOVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWFkNzg5MDQ1ZDQ5N2VmODg5MWQzYzM4MWY5OTI1ZDRhNzQ3ZjNjM2VmOTA1MDQwNGU3ZGM0Y2I1MDkwY2MzNWUmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.GlUL5Oqk9-SqLH9chSQxxm9sI3tkbqNoQ8To8UjrawE) 在docker里可以运行，但是没法播放设置页面中的播放链接，选中设备那里是空的，是不是没成功？怎么调整？
> > 
> > 
> > 设置页面输入小米的账号密码后，再勾选一个设备。
> 
> 设置页面里没有勾选的选项
> 
> > > ![image](https://private-user-images.githubusercontent.com/73272860/369268116-4fed0748-5497-47b5-ac9c-9cbc1d826a94.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjY4MTQ0MzksIm5iZiI6MTcyNjgxNDEzOSwicGF0aCI6Ii83MzI3Mjg2MC8zNjkyNjgxMTYtNGZlZDA3NDgtNTQ5Ny00N2I1LWFjOWMtOWNiYzFkODI2YTk0LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDA5MjAlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwOTIwVDA2MzUzOVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWFkNzg5MDQ1ZDQ5N2VmODg5MWQzYzM4MWY5OTI1ZDRhNzQ3ZjNjM2VmOTA1MDQwNGU3ZGM0Y2I1MDkwY2MzNWUmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.GlUL5Oqk9-SqLH9chSQxxm9sI3tkbqNoQ8To8UjrawE) 在docker里可以运行，但是没法播放设置页面中的播放链接，选中设备那里是空的，是不是没成功？怎么调整？
> > 
> > 
> > 设置页面输入小米的账号密码后，再勾选一个设备。
> 
> ![image](https://private-user-images.githubusercontent.com/73272860/369589185-a4185b88-89b2-4682-b931-47fa39463c0c.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MjY4ODUzMzgsIm5iZiI6MTcyNjg4NTAzOCwicGF0aCI6Ii83MzI3Mjg2MC8zNjk1ODkxODUtYTQxODViODgtODliMi00NjgyLWI5MzEtNDdmYTM5NDYzYzBjLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDA5MjElMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQwOTIxVDAyMTcxOFomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTA3NzU1YjcwYWY2ZTNmYTRiZTY4NGU0MDMyZGIxNDBjYjE3ZThhNzdjMmJhYWEzYjE4MjJjMjgyNzk4OWVlODUmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.0MwpJNlNUU9XhjwOFcLisXQHFTAjsU8lscTm5-sfZpw) 设置页面没有可勾选项？

![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/6bdf44b1-03cd-436f-a222-4889301affaa)

显示未检测到设备，设备型号是MDZ-25-DA

---

### 评论 20 - hanxi

@agigogo 应该是登陆失败了，局域网的手机重新登陆一下小爱音箱app吧。

---

### 评论 21 - agigogo

> @agigogo 应该是登陆失败了，局域网的手机重新登陆一下小爱音箱app吧。

一直都是用米家APP来控制小爱音箱，那要下个小爱音响APP试一试 

---

### 评论 22 - agigogo

> @agigogo 应该是登陆失败了，局域网的手机重新登陆一下小爱音箱app吧。

搞定了，小爱音箱app重新绑定就可以了。真6~

---

### 评论 23 - Tueafternoon

一首歌结束不能自动切到下一首，随机播放模式，日志中显示下一曲定时器不见了....这个是咋回事啊

---

### 评论 24 - hanxi

> 一首歌结束不能自动切到下一首，随机播放模式，日志中显示下一曲定时器不见了....这个是咋回事啊

可能是音乐文件有问题，获取歌曲长度失败，你可以把歌曲文件上传一下给我测试。

---

### 评论 25 - hanxi

或者搜下日志里有没有 不会设置下一首歌的定时器 这个

---

### 评论 26 - Tueafternoon

> 或者搜下日志里有没有 不会设置下一首歌的定时器 这个

有这个，应该是我的文件格式问题，晚上我处理一下再试试

---

### 评论 27 - zealler9560

![Screenshot_2024-10-31-23-28-57-903_com.android.chrome.jpg](https://gproxy.hanxi.cc/proxy/user-attachments/assets/541ff644-0dcd-4bda-a142-2eb4a0f94b9d)

istore系统可以拉取创建镜像，但是无法启动，错误提示见图一，求助大佬！路由器信息见图二![Screenshot_2024-10-31-23-36-42-846-edit_com.android.chrome.jpg](https://gproxy.hanxi.cc/proxy/user-attachments/assets/767383a5-8f84-471b-ab15-61e0fe9cbb22)



---

### 评论 28 - adidas004

在群晖使用docker安装的，只能通过重装来升级到最新版本吗？有快捷的一键升级吗

---

### 评论 29 - hanxi

> 在群晖使用docker安装的，只能通过重装来升级到最新版本吗？有快捷的一键升级吗

不会命令行，就用这个工具吧。 https://github.com/onlyLTY/dockerCopilot

---

### 评论 30 - adidas004

谢谢您的工具，我刚去群晖的docker上有提示直接升级，还是非常的感觉你的回答




------------------&nbsp;原始邮件&nbsp;------------------
发件人: ***@***.***&gt;; 
发送时间: 2024年11月11日(星期一) 下午4:20
收件人: ***@***.***&gt;; 
抄送: ***@***.***&gt;; ***@***.***&gt;; 
主题: Re: [hanxi/xiaomusic] 💬 FAQ问题集合 (Issue #99)














  
在群晖使用docker安装的，只能通过重装来升级到最新版本吗？有快捷的一键升级吗
  
不会命令行，就用这个工具吧。 https://github.com/onlyLTY/dockerCopilot
 
—
Reply to this email directly, view it on GitHub, or unsubscribe.
You are receiving this because you commented.Message ID: ***@***.***&gt;

---

### 评论 31 - sinojelly

运行时遇到下面问题，请问要怎么排查？
```
2024/11/21 19:27:00  xiaomusic.py: error: unrecognized arguments: .venv/bin/python3 xiaomusic.py
2024/11/21 19:27:00         [--enable_config_example]
2024/11/21 19:27:00         [--ffmpeg_location FFMPEG_LOCATION]
2024/11/21 19:27:00         [--config CONFIG]
2024/11/21 19:27:00         [--verbose]
2024/11/21 19:27:00         [--cookie COOKIE]
2024/11/21 19:27:00         [--password PASSWORD]
2024/11/21 19:27:00         [--account ACCOUNT]
2024/11/21 19:27:00         [--hardware HARDWARE]
2024/11/21 19:27:00         [--port PORT]
2024/11/21 19:27:00         [-h]
2024/11/21 19:27:00  usage: xiaomusic.py
```

---

### 评论 32 - hanxi

> 运行时遇到下面问题，请问要怎么排查？
> 
> ```
> 2024/11/21 19:27:00  xiaomusic.py: error: unrecognized arguments: .venv/bin/python3 xiaomusic.py
> 2024/11/21 19:27:00         [--enable_config_example]
> 2024/11/21 19:27:00         [--ffmpeg_location FFMPEG_LOCATION]
> 2024/11/21 19:27:00         [--config CONFIG]
> 2024/11/21 19:27:00         [--verbose]
> 2024/11/21 19:27:00         [--cookie COOKIE]
> 2024/11/21 19:27:00         [--password PASSWORD]
> 2024/11/21 19:27:00         [--account ACCOUNT]
> 2024/11/21 19:27:00         [--hardware HARDWARE]
> 2024/11/21 19:27:00         [--port PORT]
> 2024/11/21 19:27:00         [-h]
> 2024/11/21 19:27:00  usage: xiaomusic.py
> ```

看不出来

---

### 评论 33 - sinojelly

请问登录验证失败要怎么定位？小米登录邮箱，还是小米id 都报同样的错。
```
2024/11/25 0:50:44  [2024-11-25 00:50:44] [0.3.48] [ERROR] xiaomusic.py:259: /root/.mi.token file not exist
2024/11/25 0:50:44  Exception: Error https://api2.mina.mi.com/admin/v2/device_list?master=0&requestId=app_ios_: Login failed
2024/11/25 0:50:44      raise Exception(f"Error {url}: {resp}")
2024/11/25 0:50:44    File "/app/.venv/lib/python3.10/site-packages/miservice/miaccount.py", line 150, in mi_request
2024/11/25 0:50:44      return await self.account.mi_request(
2024/11/25 0:50:44    File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 49, in mina_request
2024/11/25 0:50:44      result = await self.mina_request("/admin/v2/device_list?master=" + str(master))
2024/11/25 0:50:44    File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 54, in device_list
2024/11/25 0:50:44      hardware_data = await self.mina_service.device_list()
2024/11/25 0:50:44    File "/app/xiaomusic/xiaomusic.py", line 232, in try_update_device_id
2024/11/25 0:50:44  Traceback (most recent call last):
2024/11/25 0:50:44  [2024-11-25 00:50:44] [0.3.48] [ERROR] xiaomusic.py:251: Execption Error https://api2.mina.mi.com/admin/v2/device_list?master=0&requestId=app_ios_xx: Login failed
2024/11/25 0:50:44  Exception: {'qs': '%3Fsid%3Dmicoapi%26_json%3Dtrue', 'code': 70016, 'description': '登录验证失败', 'securityStatus': 0, '_sign': 'xxx', 'sid': 'micoapi', 'result': 'error', 'captchaUrl': None, 'callback': 'https://api2.mina.mi.com/sts', 'location': '', 'pwd': 0, 'child': 0, 'desc': '登录验证失败'}
2024/11/25 0:50:44      raise Exception(resp)
2024/11/25 0:50:44    File "/app/.venv/lib/python3.10/site-packages/miservice/miaccount.py", line 69, in login
```

---

### 评论 34 - hanxi

> 请问登录验证失败要怎么定位？小米登录邮箱，还是小米id 都报同样的错。
> 
> ```
> 2024/11/25 0:50:44  [2024-11-25 00:50:44] [0.3.48] [ERROR] xiaomusic.py:259: /root/.mi.token file not exist
> 2024/11/25 0:50:44  Exception: Error https://api2.mina.mi.com/admin/v2/device_list?master=0&requestId=app_ios_: Login failed
> 2024/11/25 0:50:44      raise Exception(f"Error {url}: {resp}")
> 2024/11/25 0:50:44    File "/app/.venv/lib/python3.10/site-packages/miservice/miaccount.py", line 150, in mi_request
> 2024/11/25 0:50:44      return await self.account.mi_request(
> 2024/11/25 0:50:44    File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 49, in mina_request
> 2024/11/25 0:50:44      result = await self.mina_request("/admin/v2/device_list?master=" + str(master))
> 2024/11/25 0:50:44    File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 54, in device_list
> 2024/11/25 0:50:44      hardware_data = await self.mina_service.device_list()
> 2024/11/25 0:50:44    File "/app/xiaomusic/xiaomusic.py", line 232, in try_update_device_id
> 2024/11/25 0:50:44  Traceback (most recent call last):
> 2024/11/25 0:50:44  [2024-11-25 00:50:44] [0.3.48] [ERROR] xiaomusic.py:251: Execption Error https://api2.mina.mi.com/admin/v2/device_list?master=0&requestId=app_ios_xx: Login failed
> 2024/11/25 0:50:44  Exception: {'qs': '%3Fsid%3Dmicoapi%26_json%3Dtrue', 'code': 70016, 'description': '登录验证失败', 'securityStatus': 0, '_sign': 'xxx', 'sid': 'micoapi', 'result': 'error', 'captchaUrl': None, 'callback': 'https://api2.mina.mi.com/sts', 'location': '', 'pwd': 0, 'child': 0, 'desc': '登录验证失败'}
> 2024/11/25 0:50:44      raise Exception(resp)
> 2024/11/25 0:50:44    File "/app/.venv/lib/python3.10/site-packages/miservice/miaccount.py", line 69, in login
> ```

上面给出了4个办法都试过了吗？局域网登陆mi.com了？

---

### 评论 35 - wusemao

设置web访问登录时，账号密码设置完之后登不进去了，账号名称用的中文的可以么

---

### 评论 36 - hanxi

> 设置web访问登录时，账号密码设置完之后登不进去了，账号名称用的中文的可以么

不确定是否可以，你可以考虑setting.json里的内容，不行就修改再重启。

---

### 评论 37 - quanmao

运行后会在音乐目录下生成一个tmp文件夹，如何指定路径，因为还有其应用访问音乐路径，不希望其识别tmp目录下的音乐。

---

### 评论 38 - hanxi

> 运行后会在音乐目录下生成一个tmp文件夹，如何指定路径，因为还有其应用访问音乐路径，不希望其识别tmp目录下的音乐。

忽略目录(逗号分割) 改成 `@eaDir,tmp` 即可。

---

### 评论 39 - quanmao

> > 运行后会在音乐目录下生成一个tmp文件夹，如何指定路径，因为还有其应用访问音乐路径，不希望其识别tmp目录下的音乐。
> 
> 忽略目录(逗号分割) 改成 `@eaDir,tmp` 即可。

抱歉，是我没有说清楚，是运行xiaomusic后会在音乐路径下生成tmp文件夹， 但我同时还在用navidrome,也会访问音乐目录，他会把tmp目录下的歌曲也扫描进去，所以想移动tmp目录。 navidrome没找到在哪里可以设置，忽略这个文件夹

---

### 评论 40 - hanxi

> > > 运行后会在音乐目录下生成一个tmp文件夹，如何指定路径，因为还有其应用访问音乐路径，不希望其识别tmp目录下的音乐。
> > 
> > 
> > 忽略目录(逗号分割) 改成 `@eaDir,tmp` 即可。
> 
> 抱歉，是我没有说清楚，是运行xiaomusic后会在音乐路径下生成tmp文件夹， 但我同时还在用navidrome,也会访问音乐目录，他会把tmp目录下的歌曲也扫描进去，所以想移动tmp目录。 navidrome没找到在哪里可以设置，忽略这个文件夹

提个新 issue 吧，有空加下配置项。

---

### 评论 41 - CallEdison

![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/6724300b-ce48-44e6-9729-7b844bc0751e)
![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/e7e87adb-a54d-42dd-bfc9-f84a01116a48)
问题一：能进控制面板，进不了设置页面，容器没有log生成，我昨天已经设置好了，现在功能能正常使用，但是进不了设置页面了问题二：昨天能进的时候发现本地下载目录有歌曲，但是设置里面的全部歌曲里面没有，搜索框搜索又能搜的到。

---

### 评论 42 - hanxi

> ![image](https://private-user-images.githubusercontent.com/49771101/390617216-6724300b-ce48-44e6-9729-7b844bc0751e.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzI3NjIzOTQsIm5iZiI6MTczMjc2MjA5NCwicGF0aCI6Ii80OTc3MTEwMS8zOTA2MTcyMTYtNjcyNDMwMGItY2U0OC00NGU2LTk3MjktN2I4NDRiYzA3NTFlLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDExMjglMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQxMTI4VDAyNDgxNFomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWM3NWNkOTc4MzI2OTUxY2JkMzg2OGU1NGQ3ZWVkODA2MTIyYzE0YmY4MTRiMmVjMTAwYWJkMjBhNDc4MjgwOWMmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.BFuNPM9Ihxe0V3qhE_9JIbgt9rn_u5dC542hOLckwT8) ![image](https://private-user-images.githubusercontent.com/49771101/*********-e7e87adb-a54d-42dd-bfc9-f84a01116a48.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzI3NjIzOTQsIm5iZiI6MTczMjc2MjA5NCwicGF0aCI6Ii80OTc3MTEwMS8zOTA2MTcyOTQtZTdlODdhZGItYTU0ZC00MmRkLWJmYzktZjg0YTAxMTE2YTQ4LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDExMjglMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQxMTI4VDAyNDgxNFomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTA3OTA5YTQwODQ4ZmM0Nzg2ZGYwZTc4YmQyMWU5MDgwYjhjZTRiNzRkYzI0NDE2MzI4M2JhOWYxZDlhYjNlYzMmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.8JMoNWDzq-C9pnJy1Ru51LZbP_G5g1fAKYqSppCLKa4) 问题一：能进控制面板，进不了设置页面，容器没有log生成，我昨天已经设置好了，现在功能能正常使用，但是进不了设置页面了问题二：昨天能进的时候发现本地下载目录有歌曲，但是设置里面的全部歌曲里面没有，搜索框搜索又能搜的到。

问题一：打不开的地址是哪个？
问题二：可以点击刷新列表按钮试试。

---

### 评论 43 - huahua-er

是关闭了网络搜索了吗？现在的搜索只有本地数据没有网络歌曲了?

---

### 评论 44 - CallEdison

默认主题有歌曲


pure主题没有歌曲

xmusicPlayer也没有歌曲


&nbsp;


Edison
***@***.***



&nbsp;




------------------&nbsp;原始邮件&nbsp;------------------
发件人: ***@***.***&gt;; 
发送时间: 2024年11月30日(星期六) 上午6:26
收件人: ***@***.***&gt;; 
抄送: ***@***.***&gt;; ***@***.***&gt;; 
主题: Re: [hanxi/xiaomusic] 💬 FAQ问题集合 (Issue #99)














 
是关闭了网络搜索了吗？现在的搜索只有本地数据没有网络歌曲了?
 
—
Reply to this email directly, view it on GitHub, or unsubscribe.
You are receiving this because you commented.Message ID: ***@***.***&gt;

---

### 评论 45 - hanxi

> 是关闭了网络搜索了吗？现在的搜索只有本地数据没有网络歌曲了?

等 yt-dlp 修复。

---

### 评论 46 - hanxi

> 默认主题有歌曲 pure主题没有歌曲 xmusicPlayer也没有歌曲 &nbsp; Edison ***@***.*** &nbsp;
> […](#)
> ------------------&nbsp;原始邮件&nbsp;------------------ 发件人: ***@***.***&gt;; 发送时间: 2024年11月30日(星期六) 上午6:26 收件人: ***@***.***&gt;; 抄送: ***@***.***&gt;; ***@***.***&gt;; 主题: Re: [hanxi/xiaomusic] 💬 FAQ问题集合 (Issue #99) 是关闭了网络搜索了吗？现在的搜索只有本地数据没有网络歌曲了? — Reply to this email directly, view it on GitHub, or unsubscribe. You are receiving this because you commented.Message ID: ***@***.***&gt;

需要刷新缓存

---

### 评论 47 - like1020

![Screenshot_2024-12-03-06-58-52-853_com yjllq kito](https://gproxy.hanxi.cc/proxy/user-attachments/assets/5d37bfa6-d63a-46b1-b5a8-554814fc7dce)
请教一下，本地列表歌单里的歌曲即便设置为全部循环或随机播放，依然是不断地单曲循环，只能自己手动点下一首，请问是什么情况？

---

### 评论 48 - tchgtr

请问网络搜索功能修复了吗？感谢！

---

### 评论 49 - hanxi

> 请问网络搜索功能修复了吗？感谢！

修复了。

---

### 评论 50 - tchgtr

> > 请问网络搜索功能修复了吗？感谢！
> 
> 修复了。

我已经更新到最新版0.3.55，音箱是LX04触屏音箱，但是使用指令对小爱说“播放歌曲。。。“还是通过音箱绑定的QQ音乐播放，解绑了QQ音乐账号，就会提示先让我绑定账号（包括播放本地歌曲指令），感觉音箱还没跟docker连接上（已经重启过容器）。但是通过8090端口进入后台，搜索歌曲，播放，调节声音大小，单曲、随机所有的功能都可以通过按键实现，唯独不能通过语音跟小爱对话播放我指定的内容，请问这个是什么问题，感谢！

---

### 评论 51 - hanxi

> > > 请问网络搜索功能修复了吗？感谢！
> > 
> > 
> > 修复了。
> 
> 我已经更新到最新版0.3.55，音箱是LX04触屏音箱，但是使用指令对小爱说“播放歌曲。。。“还是通过音箱绑定的QQ音乐播放，解绑了QQ音乐账号，就会提示先让我绑定账号（包括播放本地歌曲指令），感觉音箱还没跟docker连接上（已经重启过容器）。但是通过8090端口进入后台，搜索歌曲，播放，调节声音大小，单曲、随机所有的功能都可以通过按键实现，唯独不能通过语音跟小爱对话播放我指定的内容，请问这个是什么问题，感谢！

贴下你的 setting.json 文件看看吧，把里面的账号密码删除。

---

### 评论 52 - tchgtr

> > > > 请问网络搜索功能修复了吗？感谢！
> > > 
> > > 
> > > 修复了。
> > 
> > 
> > 我已经更新到最新版0.3.55，音箱是LX04触屏音箱，但是使用指令对小爱说“播放歌曲。。。“还是通过音箱绑定的QQ音乐播放，解绑了QQ音乐账号，就会提示先让我绑定账号（包括播放本地歌曲指令），感觉音箱还没跟docker连接上（已经重启过容器）。但是通过8090端口进入后台，搜索歌曲，播放，调节声音大小，单曲、随机所有的功能都可以通过按键实现，唯独不能通过语音跟小爱对话播放我指定的内容，请问这个是什么问题，感谢！
> 
> 贴下你的 setting.json 文件看看吧，把里面的账号密码删除。

{
  "account": "",
  "password": "",
  "mi_did": "*********",
  "miio_tts_command": "",
  "cookie": "",
  "verbose": false,
  "music_path": "music",
  "temp_path": "music/tmp",
  "download_path": "music/download",
  "conf_path": "conf",
  "cache_dir": "cache",
  "hostname": "**************",
  "port": 8090,
  "public_port": 0,
  "proxy": "",
  "search_prefix": "bilisearch:",
  "ffmpeg_location": "./ffmpeg/bin",
  "active_cmd": "play,set_play_type_rnd,playlocal,play_music_list,play_music_list_index,stop_after_minute,stop",
  "exclude_dirs": "@eaDir,tmp",
  "music_path_depth": 10,
  "disable_httpauth": true,
  "httpauth_username": "",
  "httpauth_password": "",
  "music_list_url": "",
  "music_list_json": "",
  "custom_play_list_json": "",
  "disable_download": false,
  "key_word_dict": {
    "下一首": "play_next",
    "上一首": "play_prev",
    "单曲循环": "set_play_type_one",
    "全部循环": "set_play_type_all",
    "随机播放": "set_play_type_rnd",
    "单曲播放": "set_play_type_sin",
    "顺序播放": "set_play_type_seq",
    "分钟后关机": "stop_after_minute",
    "刷新列表": "gen_music_list",
    "加入收藏": "add_to_favorites",
    "收藏歌曲": "add_to_favorites",
    "取消收藏": "del_from_favorites",
    "播放列表第": "play_music_list_index",
    "播放本地歌曲": "playlocal",
    "本地播放歌曲": "playlocal",
    "播放歌曲": "play",
    "放歌曲": "play",
    "关机": "stop",
    "暂停": "stop",
    "停止": "stop",
    "停止播放": "stop",
    "播放列表": "play_music_list",
    "播放歌单": "play_music_list",
    "测试自定义口令": "exec#code1(\"hello\")",
    "测试链接": "exec#httpget(\"https://github.com/hanxi/xiaomusic\")"
  },
  "key_match_order": [
    "分钟后关机",
    "下一首",
    "上一首",
    "单曲循环",
    "全部循环",
    "随机播放",
    "单曲播放",
    "顺序播放",
    "关机",
    "刷新列表",
    "播放列表第",
    "播放列表",
    "加入收藏",
    "收藏歌曲",
    "取消收藏",
    "播放本地歌曲",
    "本地播放歌曲",
    "播放歌曲",
    "放歌曲",
    "暂停",
    "停止",
    "停止播放",
    "播放歌单",
    "测试自定义口令",
    "测试链接"
  ],
  "use_music_api": false,
  "use_music_audio_id": "1582971365183456177",
  "use_music_id": "355454500",
  "log_file": "xiaomusic.log.txt",
  "fuzzy_match_cutoff": 0.6,
  "enable_fuzzy_match": true,
  "stop_tts_msg": "收到,再见",
  "enable_config_example": false,
  "keywords_playlocal": "播放本地歌曲,本地播放歌曲",
  "keywords_play": "播放歌曲,放歌曲",
  "keywords_stop": "关机,暂停,停止,停止播放",
  "keywords_playlist": "播放列表,播放歌单",
  "user_key_word_dict": {
    "测试自定义口令": "exec#code1(\"hello\")",
    "测试链接": "exec#httpget(\"https://github.com/hanxi/xiaomusic\")"
  },
  "enable_force_stop": false,
  "devices": {
    "*********": {
      "did": "*********",
      "device_id": "60b8f875-4101-416a-9278-4d4170929b4d",
      "hardware": "LX04",
      "name": "小爱触屏音箱",
      "play_type": 1,
      "cur_music": "七里香",
      "cur_playlist": "临时搜索列表"
    }
  },
  "group_list": "",
  "remove_id3tag": false,
  "convert_to_mp3": false,
  "delay_sec": 3,
  "continue_play": false,
  "pull_ask_sec": 1,
  "crontab_json": "",
  "enable_yt_dlp_cookies": false,
  "get_ask_by_mina": true,
  "play_type_one_tts_msg": "已经设置为单曲循环",
  "play_type_all_tts_msg": "已经设置为全部循环",
  "play_type_rnd_tts_msg": "已经设置为随机播放",
  "play_type_sin_tts_msg": "已经设置为单曲播放",
  "play_type_seq_tts_msg": "已经设置为顺序播放",
  "recently_added_playlist_len": 50
}

---

### 评论 53 - hanxi

get_ask_by_mina 【特殊型号获取对话记录】这个需要设置为 false

---

### 评论 54 - tchgtr

> get_ask_by_mina 【特殊型号获取对话记录】这个需要设置为 false

已经重新设置并重启容器，尝试还是不行，会不会是这个触屏音箱不支持？

---

### 评论 55 - tchgtr

> > get_ask_by_mina 【特殊型号获取对话记录】这个需要设置为 false
> 
> 已经重新设置并重启容器，尝试还是不行，会不会是这个触屏音箱不支持？

或者是在创建容器的时候，我的小米账号填写了我的手机号码，应该需要填写实际的小米账号？因为手机号码对应的小米账号，和输入对应的密码，都是可以登陆的，但是我不知道有什么区别，就把手机号码输入进去了

---

### 评论 56 - tchgtr

> > > get_ask_by_mina 【特殊型号获取对话记录】这个需要设置为 false
> > 
> > 
> > 已经重新设置并重启容器，尝试还是不行，会不会是这个触屏音箱不支持？
> 
> 或者是在创建容器的时候，我的小米账号填写了我的手机号码，应该需要填写实际的小米账号？因为手机号码对应的小米账号，和输入对应的密码，都是可以登陆的，但是我不知道有什么区别，就把手机号码输入进去了

我重新创建容器，问题解决了，主要是两个地方改变了，一个是上面的小米账号的问题，另外一个是关于conf和music这两个文件夹，这次我都把他们放在/container/xiaomusic下，即/container/xiaomusic/conf(music)，分别对应两个容器和主机app/conf(music)，问题就解决了。之前我是分开在两个文件夹的。

---

### 评论 57 - tianting123

0 B的APE文件， 建议直接跳过播放

---

### 评论 58 - oklrc

新人请教：使用DOCKER镜像 或者 composer安装 如何升级到最新版本呢？是删除镜像再重新拉取吗

---

### 评论 59 - hanxi

> 新人请教：使用DOCKER镜像 或者 composer安装 如何升级到最新版本呢？是删除镜像再重新拉取吗

```
docker compose pull
docker compose up -d
```

---

### 评论 60 - oklrc

更新到最新版后，找不到小爱同学了

日志：
[2024-12-14 11:34:54] [0.3.57] [ERROR] xiaomusic.py:1283: Execption 'NoneType' object has no attribute 'device_list'
Traceback (most recent call last):
  File "/app/xiaomusic/xiaomusic.py", line 1281, in getalldevices
    device_list = await self.mina_service.device_list()
AttributeError: 'NoneType' object has no attribute 'device_list'
[2024-12-14 11:34:54] [0.3.57] [INFO] httpserver.py:246: getsetting device_list: []
[2024-12-14 11:34:54] [0.3.57] [INFO] httpserver.py:246: getsetting device_list: []
[2024-12-14 11:34:54] [0.3.57] [INFO] httpserver.py:246: getsetting device_list: []


---

### 评论 61 - hanxi

> 更新到最新版后，找不到小爱同学了
> 
> 日志： [2024-12-14 11:34:54] [0.3.57] [ERROR] xiaomusic.py:1283: Execption 'NoneType' object has no attribute 'device_list' Traceback (most recent call last): File "/app/xiaomusic/xiaomusic.py", line 1281, in getalldevices device_list = await self.mina_service.device_list() AttributeError: 'NoneType' object has no attribute 'device_list' [2024-12-14 11:34:54] [0.3.57] [INFO] httpserver.py:246: getsetting device_list: [] [2024-12-14 11:34:54] [0.3.57] [INFO] httpserver.py:246: getsetting device_list: [] [2024-12-14 11:34:54] [0.3.57] [INFO] httpserver.py:246: getsetting device_list: []

登陆掉了吧，重新登陆一下。

---

### 评论 62 - sprout-cooler

登录后，显示无设备；上面提到的方法都试过了。没有用，日志显示
  File "/app/xiaomusic/xiaomusic.py", line 1270, in reinit

    await self.init_all_data(self.session)

AttributeError: 'XiaoMusic' object has no attribute 'session'

---

### 评论 63 - fu-yuming

今天更新后，找不到设备了。重新输账号密码保存了，怎么都不行。重新部署也找不到。

---

### 评论 64 - zhiquanchi

> 今天更新后，找不到设备了。重新输账号密码保存了，怎么都不行。重新部署也找不到。

用默认主题试下

---

### 评论 65 - hanxi

可以更新试试。

---

### 评论 66 - lijichao2018

我点保存，网页显示[object Object]，不知道啥情况

---

### 评论 67 - hanxi

> 我点保存，网页显示[object Object]，不知道啥情况

0.3.58版本吗？

---

### 评论 68 - lijichao2018

0.3.57




&nbsp;


该邮件从移动设备发送


&nbsp;




------------------&nbsp;原始邮件&nbsp;------------------
发件人: ***@***.***&gt;; 
发送时间: 2024年12月15日(星期天) 中午12:17
收件人: ***@***.***&gt;; 
抄送: ***@***.***&gt;; ***@***.***&gt;; 
主题: Re: [hanxi/xiaomusic] 💬 FAQ问题集合 (Issue #99)














  
我点保存，网页显示[object Object]，不知道啥情况
  
0.3.58版本吗？
 
—
Reply to this email directly, view it on GitHub, or unsubscribe.
You are receiving this because you commented.Message ID: ***@***.***&gt;

---

### 评论 69 - hanxi

那你更新吧。58应该是修复了。

---

### 评论 70 - oklrc

> 那你更新吧。58应该是修复了。

新版本确实好了，我昨天遇到的问题和楼上是一样的

---

### 评论 71 - lijichao2018

绿联的NAS怎么更新到58版本啊，没找到，要用口令吗？

---

### 评论 72 - zjzj52

容器一直重启，网页后台也进不去

---

### 评论 73 - hanxi

> 容器一直重启，网页后台也进不去

这种情况有日志吗？可能是端口冲突？

---

### 评论 74 - BladPit

不知道有没有用unraid系统NAS的朋友？
我这里使用docker安装xiaomusic有一个很奇怪的现象，不知道有没有大佬能够解惑：

我的unraid系统版本是6.8.2，通过docker安装后发现，如果没有开启特权模式（Privileged），那么（从内网）访问webGUI的时候，在首页选择了主题之后会直接网页报错【Internal Server Error】+显示空白页面，查看docker运行日志，是正常的，没有什么相关的报错：
`2025-01-04 00:29:06,055 CRIT Supervisor is running as root. Privileges were not dropped because no user is specified in the config file. If you intend to run as root, you can set user=root in the config file to avoid this message.

2025-01-04 00:29:06,064 INFO RPC interface 'supervisor' initialized

2025-01-04 00:29:06,064 CRIT Server 'unix_http_server' running without any HTTP authentication checking

2025-01-04 00:29:06,065 INFO supervisord started with pid 1

2025-01-04 00:29:07,068 INFO spawned: 'xiaomusic' with pid 6`

然而如果开启特权模式运行docker，则会完全正常，webGUI能够正常访问。感觉非常疑惑，不知道是什么原因。
这个问题之前在安装docker版迅雷的时候有类似情况，必须开启特权模式迅雷的docker才能启动，但迅雷的docker应该是所有人都如此，但xiaomusic的这种情况，我询问过其他unraid群友，似乎没有遇到该问题。

---

### 评论 75 - ZYT1601

容器一直重启，日志一直循环一段
Unlinking stale socket /var/run/supervisor.sock

/usr/lib/python3/dist-packages/supervisor/options.py:474: UserWarning: Supervisord is running as root and it is searching for its configuration file in default locations (including its current working directory); you probably want to specify a "-c" argument specifying an absolute path to a configuration file for improved security.

  self.warnings.warn(

Unlinking stale socket /var/run/supervisor.sock

---

### 评论 76 - hanxi

@ZYT1601 试试看这个方法： /issues/348.html#issuecomment-2568722472

---

### 评论 77 - ZYT1601

可以了，飞牛把容器设置里面的命令全部删掉也是一个道理


---

### 评论 78 - ZYT1601

> 可以了，飞牛把容器设置里面的命令全部删掉也是一个道理

好像不能一劳永逸


---

### 评论 79 - hanxi

> > 可以了，飞牛把容器设置里面的命令全部删掉也是一个道理
> 
> 好像不能一劳永逸

等后续版本吧。

---

### 评论 80 - Ivysaur-91

有个问题请教下，建立容器的时候指定了nas本地的位置映射到容器的音乐路径，但是在nas路径防止文件无法被识别出来。
![屏幕截图 2025-01-05 183306](https://gproxy.hanxi.cc/proxy/user-attachments/assets/c3a8a107-1fb9-456d-8f03-da0d03b3d770)
直接在xiaomusic里面下载音乐是可以播放的，进入到docker里面的路径也是有音乐文件的
![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/fdb6ea7a-8ccf-4580-a983-b793c591869b)

建容器的命令如下：
docker run -p 58090:8090 -e XIAOMUSIC_PUBLIC_PORT=58090 -v /Public/Music/[[xiaomusic]]:/app/music -v /Public/Music/[[xiaomusic]]:/app/conf m.daocloud.io/docker.io/hanxi/xiaomusic


---

### 评论 81 - hanxi

> 有个问题请教下，建立容器的时候指定了nas本地的位置映射到容器的音乐路径，但是在nas路径防止文件无法被识别出来。 ![屏幕截图 2025-01-05 183306](https://private-user-images.githubusercontent.com/*********/400193889-c3a8a107-1fb9-456d-8f03-da0d03b3d770.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzYwNzY2MDYsIm5iZiI6MTczNjA3NjMwNiwicGF0aCI6Ii8xOTM3ODcwOTEvNDAwMTkzODg5LWMzYThhMTA3LTFmYjktNDU2ZC04ZjAzLWRhMGQwM2IzZDc3MC5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwMTA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDEwNVQxMTI1MDZaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT1kMTA2YmM4ZDI4MTk1OWFjZTAxZTZlODE1YmYwMTk4ZDE3NmMyNzI1ZDVmYjNhOWVhMTdhZTJjMDEzYjM2NjllJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.45ZS_c09oRK43CTcgUJTEbzXNDRUW0fSpWtw4PMUn3g) 直接在xiaomusic里面下载音乐是可以播放的，进入到docker里面的路径也是有音乐文件的 ![image](https://private-user-images.githubusercontent.com/*********/*********-fdb6ea7a-8ccf-4580-a983-b793c591869b.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzYwNzY2MDYsIm5iZiI6MTczNjA3NjMwNiwicGF0aCI6Ii8xOTM3ODcwOTEvNDAwMTk0MTI5LWZkYjZlYTdhLThjY2YtNDU4MC1hOTgzLWI3OTNjNTkxODY5Yi5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwMTA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDEwNVQxMTI1MDZaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT1mMWMyYmZmNWFjMzU5Yzg3MDk5N2M5MTMyMmE0ZDJlYmY5YWY4OGU3MzhiYmVkMDYyNjFjZGQzNTUyYWM4MzI0JlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.AmsxpldZ738tCXE2y_2AQuN14I4FERkOjn23JFrnRuM)
> 
> 建容器的命令如下： docker run -p 58090:8090 -e XIAOMUSIC_PUBLIC_PORT=58090 -v /Public/Music/[[xiaomusic]]:/app/music -v /Public/Music/[[xiaomusic]]:/app/conf m.daocloud.io/docker.io/hanxi/xiaomusic

为啥路径要加方括号？另外两个路径不能重复。

---

### 评论 82 - Ivysaur-91

> > 有个问题请教下，建立容器的时候指定了nas本地的位置映射到容器的音乐路径，但是在nas路径防止文件无法被识别出来。 ![屏幕截图 2025-01-05 183306](https://private-user-images.githubusercontent.com/*********/400193889-c3a8a107-1fb9-456d-8f03-da0d03b3d770.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzYwNzY2MDYsIm5iZiI6MTczNjA3NjMwNiwicGF0aCI6Ii8xOTM3ODcwOTEvNDAwMTkzODg5LWMzYThhMTA3LTFmYjktNDU2ZC04ZjAzLWRhMGQwM2IzZDc3MC5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwMTA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDEwNVQxMTI1MDZaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT1kMTA2YmM4ZDI4MTk1OWFjZTAxZTZlODE1YmYwMTk4ZDE3NmMyNzI1ZDVmYjNhOWVhMTdhZTJjMDEzYjM2NjllJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.45ZS_c09oRK43CTcgUJTEbzXNDRUW0fSpWtw4PMUn3g) 直接在xiaomusic里面下载音乐是可以播放的，进入到docker里面的路径也是有音乐文件的 ![image](https://private-user-images.githubusercontent.com/*********/*********-fdb6ea7a-8ccf-4580-a983-b793c591869b.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzYwNzY2MDYsIm5iZiI6MTczNjA3NjMwNiwicGF0aCI6Ii8xOTM3ODcwOTEvNDAwMTk0MTI5LWZkYjZlYTdhLThjY2YtNDU4MC1hOTgzLWI3OTNjNTkxODY5Yi5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwMTA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDEwNVQxMTI1MDZaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT1mMWMyYmZmNWFjMzU5Yzg3MDk5N2M5MTMyMmE0ZDJlYmY5YWY4OGU3MzhiYmVkMDYyNjFjZGQzNTUyYWM4MzI0JlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.AmsxpldZ738tCXE2y_2AQuN14I4FERkOjn23JFrnRuM)
> > 建容器的命令如下： docker run -p 58090:8090 -e XIAOMUSIC_PUBLIC_PORT=58090 -v /Public/Music/[[xiaomusic]]:/app/music -v /Public/Music/[[xiaomusic]]:/app/conf m.daocloud.io/docker.io/hanxi/xiaomusic
> 
> 为啥路径要加方括号？另外两个路径不能重复。

方括号是为了让文件夹在第一位，重新安装容器，路径不一样后还是一样的问题：

![屏幕截图 2025-01-06 214204](https://gproxy.hanxi.cc/proxy/user-attachments/assets/3bf283cc-c951-433b-95d9-dda6abeea7b4)


语句：
docker run -p 58090:8090 -e XIAOMUSIC_PUBLIC_PORT=58090 -v /Public/Music/xiaomusic/music:/app/music -v /Public/Music/xiaomusic/config:/app/conf m.daocloud.io/docker.io/hanxi/xiaomusic

---

### 评论 83 - Ivysaur-91

> > > 有个问题请教下，建立容器的时候指定了nas本地的位置映射到容器的音乐路径，但是在nas路径防止文件无法被识别出来。 ![屏幕截图 2025-01-05 183306](https://private-user-images.githubusercontent.com/*********/400193889-c3a8a107-1fb9-456d-8f03-da0d03b3d770.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzYwNzY2MDYsIm5iZiI6MTczNjA3NjMwNiwicGF0aCI6Ii8xOTM3ODcwOTEvNDAwMTkzODg5LWMzYThhMTA3LTFmYjktNDU2ZC04ZjAzLWRhMGQwM2IzZDc3MC5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwMTA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDEwNVQxMTI1MDZaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT1kMTA2YmM4ZDI4MTk1OWFjZTAxZTZlODE1YmYwMTk4ZDE3NmMyNzI1ZDVmYjNhOWVhMTdhZTJjMDEzYjM2NjllJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.45ZS_c09oRK43CTcgUJTEbzXNDRUW0fSpWtw4PMUn3g) 直接在xiaomusic里面下载音乐是可以播放的，进入到docker里面的路径也是有音乐文件的 ![image](https://private-user-images.githubusercontent.com/*********/*********-fdb6ea7a-8ccf-4580-a983-b793c591869b.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzYwNzY2MDYsIm5iZiI6MTczNjA3NjMwNiwicGF0aCI6Ii8xOTM3ODcwOTEvNDAwMTk0MTI5LWZkYjZlYTdhLThjY2YtNDU4MC1hOTgzLWI3OTNjNTkxODY5Yi5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwMTA1JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDEwNVQxMTI1MDZaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT1mMWMyYmZmNWFjMzU5Yzg3MDk5N2M5MTMyMmE0ZDJlYmY5YWY4OGU3MzhiYmVkMDYyNjFjZGQzNTUyYWM4MzI0JlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.AmsxpldZ738tCXE2y_2AQuN14I4FERkOjn23JFrnRuM)
> > > 建容器的命令如下： docker run -p 58090:8090 -e XIAOMUSIC_PUBLIC_PORT=58090 -v /Public/Music/[[xiaomusic]]:/app/music -v /Public/Music/[[xiaomusic]]:/app/conf m.daocloud.io/docker.io/hanxi/xiaomusic
> > 
> > 
> > 为啥路径要加方括号？另外两个路径不能重复。
> 
> 方括号是为了让文件夹在第一位，重新安装容器，路径不一样后还是一样的问题：
> 
> ![屏幕截图 2025-01-06 214204](https://private-user-images.githubusercontent.com/*********/400439202-3bf283cc-c951-433b-95d9-dda6abeea7b4.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzYxNzEzMjIsIm5iZiI6MTczNjE3MTAyMiwicGF0aCI6Ii8xOTM3ODcwOTEvNDAwNDM5MjAyLTNiZjI4M2NjLWM5NTEtNDMzYi05NWQ5LWRkYTZhYmVlYTdiNC5wbmc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjUwMTA2JTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI1MDEwNlQxMzQzNDJaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT02MTExNDE3MmFkMDFlYzFlZTRlMTg0OTdiMTQ3ODY0MjIxODlmMjEzMjUyMWUwYmNjYWNkZGEzNTA0MDIyYWIxJlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.cdKynorOPxYE9nBrETp-_E98B6D1cVpITIUzRdEu49M)
> 
> 语句： docker run -p 58090:8090 -e XIAOMUSIC_PUBLIC_PORT=58090 -v /Public/Music/xiaomusic/music:/app/music -v /Public/Music/xiaomusic/config:/app/conf m.daocloud.io/docker.io/hanxi/xiaomusic

![屏幕截图 2025-01-06 215510](https://gproxy.hanxi.cc/proxy/user-attachments/assets/cc7e218c-f215-4c54-9c2e-aea7be692e22)
yi以为是文件夹关联失败了，但是进入容器看应该是成功的

---

### 评论 84 - kern111

本地歌曲如何创建歌单？我的歌曲全在/volumeUSB1/usbshare/Music/这个目录下，想手动设置类别播放

---

### 评论 85 - hanxi

> 本地歌曲如何创建歌单？我的歌曲全在/volumeUSB1/usbshare/Music/这个目录下，想手动设置类别播放

目前的方法就是自己整理分类好目录，或者去小程序里建歌单。

---

### 评论 86 - wjcroom

说, 加入收藏,  或 收藏歌曲  指令.  小爱说没有播放歌曲, 当前肯定在播放.
但是无法执行这个操作

---

### 评论 87 - hanxi

> 说, 加入收藏, 或 收藏歌曲 指令. 小爱说没有播放歌曲, 当前肯定在播放. 但是无法执行这个操作

那个是小爱自带的回答，没影响。

---

### 评论 88 - wjcroom

> > 说, 加入收藏, 或 收藏歌曲 指令. 小爱说没有播放歌曲, 当前肯定在播放. 但是无法执行这个操作
> 
> 那个是小爱自带的回答，没影响。

但是无法起到效果,收藏夹不会加入当前歌曲.  只能在web端,点一下收藏,才能加入.
语音命令,都是忽略的, setting.json能看到,这两个命令,定义
 "加入收藏": "add_to_favorites",
    "收藏歌曲": "add_to_favorites",
后面需要在附加歌名吗.  

ZZZZZZ最后发现是没刷新页面, 收藏没刷新出来. 语音命令是有效的.

---

### 评论 89 - alvin1234579

網頁播放正常,但是語音口令無法正常搜尋撥放本地音樂,還是會撥QQ音樂試聽版.偶而會亂撥不相干的歌,docker和小米音箱都重啟過了

[xiaomusic (13).txt](https://github.com/user-attachments/files/18867950/xiaomusic.13.txt)

---

### 评论 90 - hanxi

@alvin1234579 看日志是没啥问题的，多试几次，用播放歌曲开头。

---

### 评论 91 - alvin1234579

抱歉剛剛有語音撥放歌曲成功但是撥幾秒就停止了,如果搜尋不到本地音樂不是應該yt-dlp下載嗎,一直會跳QQ音樂試聽版

[xiaomusic (16).txt](https://github.com/user-attachments/files/18868240/xiaomusic.16.txt)



---

### 评论 92 - hanxi

@alvin1234579 下载歌曲是需要时间的，看日志是在下载中了。QQ音乐试听只是会语音提示，下载成功后会播放下载的歌曲的。

---

### 评论 93 - alvin1234579

> 抱歉剛剛有語音撥放歌曲成功但是撥幾秒就停止了,如果搜尋不到本地音樂不是應該yt-dlp下載嗎,一直會跳QQ音樂試聽版
> 
> [xiaomusic (16).txt](https://github.com/user-attachments/files/18868240/xiaomusic.16.txt)

已經找到原因了語音搜索會用簡體搜尋,曲名如果是繁體會搜尋不到,還有數字會用中文搜尋像是說2024會搜尋二零二四的檔名所以2024搜尋不到,這有修正的方法嗎

---

### 评论 94 - hanxi

@alvin1234579 你提个issue吧，算是个bug吧，应该处理了简体和繁体才对。

---

### 评论 95 - laolihb

我的安装运行都很顺利，但目前只能播放本地（nas音乐文件夹）的音乐，本地没有的无法播放，可以看到歌词滚动和“下载中”的语音提示，但之后就静默无声，下载文件夹空白，折腾了很多次没有解决，实在弄不好了，请楼主指导一哈，谢谢啦。
（我平时无法登录这里，只能借用朋友的饿饭临时用一哈）

---

### 评论 96 - hanxi

> 我的安装运行都很顺利，但目前只能播放本地（nas音乐文件夹）的音乐，本地没有的无法播放，可以看到歌词滚动和“下载中”的语音提示，但之后就静默无声，下载文件夹空白，折腾了很多次没有解决，实在弄不好了，请楼主指导一哈，谢谢啦。 （我平时无法登录这里，只能借用朋友的饿饭临时用一哈）

可以加微信群或者QQ群。

---

### 评论 97 - wjcroom

@hanxi  我做了一个 自定义语音播放nas电影的HA自动化。 怎么样 取消小爱不能执行的操作。这种默认回答呢。 我的指令搜电视* 。虽然后台执行了。 但是小爱的默认回复总是出现。 个人训练，无法通配符

---

### 评论 98 - hanxi

> [@hanxi](https://github.com/hanxi) 我做了一个 自定义语音播放nas电影的HA自动化。 怎么样 取消小爱不能执行的操作。这种默认回答呢。 我的指令搜电视* 。虽然后台执行了。 但是小爱的默认回复总是出现。 个人训练，无法通配符

没啥办法，有些型号就是无法打断小爱说话。

---

### 评论 99 - wjcroom

> > [@hanxi](https://github.com/hanxi) 我做了一个 自定义语音播放nas电影的HA自动化。 怎么样 取消小爱不能执行的操作。这种默认回答呢。 我的指令搜电视* 。虽然后台执行了。 但是小爱的默认回复总是出现。 个人训练，无法通配符
> 
> 没啥办法，有些型号就是无法打断小爱说话。

我也用搜索播放，这样类似的指令呢？  它就不会随便带来干涉。  只是这和 xiaomusic重合了啊。 还有类似的这样开始的吗。

---

### 评论 100 - eleght2000

能不能播放网络磁盘的歌曲  比如FTP 或者SMB共享的磁盘内的歌曲

---

### 评论 101 - hanxi

> 能不能播放网络磁盘的歌曲 比如FTP 或者SMB共享的磁盘内的歌曲

可以用rclone挂载

---

### 评论 102 - KagurazakaAsahi

作者你好，我出现了上述“本机有声音，小爱音箱没声音，请问什么原因？”这个问题，按顺序排查，默认测试链接正常，容器日志中音乐链接也正常播放，但测试链接中如果填写我的容器日志中音乐链接，也就是本地音源就无法播放，换了十几个mp3音源，均无法正常播放，小爱总说网络错误
`[2025-04-26 00:24:09] [0.3.78] [INFO] analytics.py:105: umami Status: 400


[00:25:03] [0.3.78] [INFO] 172.17.0.1:48048 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:25:03] [0.3.78] [INFO] 172.17.0.1:48054 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:25:03] [0.3.78] [INFO] 172.17.0.1:48048 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:25:03] [0.3.78] [INFO] 172.17.0.1:48054 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:25:03] [0.3.78] [INFO] 172.17.0.1:48048 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[2025-04-26 00:25:03] [0.3.78] [INFO] xiaomusic.py:430: try get_filename. filename:music/Kalafina - oblivious.mp3


[2025-04-26 00:25:03] [0.3.78] [INFO] xiaomusic.py:536: get_music_url local music. name:Kalafina - oblivious, filename:Kalafina - oblivious.mp3


[00:25:03] [0.3.78] [INFO] 172.17.0.1:48064 - "GET /musicinfo?name=Kalafina%20-%20oblivious&musictag=true HTTP/1.1" 200


[00:25:03] [0.3.78] [INFO] 172.17.0.1:48054 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:25:03] [0.3.78] [INFO] 172.17.0.1:48064 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:26:03] [0.3.78] [INFO] 172.17.0.1:39316 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:26:03] [0.3.78] [INFO] 172.17.0.1:39332 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:26:03] [0.3.78] [INFO] 172.17.0.1:39334 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:26:03] [0.3.78] [INFO] 172.17.0.1:39316 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[2025-04-26 00:26:03] [0.3.78] [INFO] xiaomusic.py:430: try get_filename. filename:music/Kalafina - oblivious.mp3


[2025-04-26 00:26:03] [0.3.78] [INFO] xiaomusic.py:536: get_music_url local music. name:Kalafina - oblivious, filename:Kalafina - oblivious.mp3


[00:26:03] [0.3.78] [INFO] 172.17.0.1:39332 - "GET /musicinfo?name=Kalafina%20-%20oblivious&musictag=true HTTP/1.1" 200


[00:26:03] [0.3.78] [INFO] 172.17.0.1:39354 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:26:03] [0.3.78] [INFO] 172.17.0.1:39344 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:26:03] [0.3.78] [INFO] 172.17.0.1:39370 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:27:03] [0.3.78] [INFO] 172.17.0.1:51656 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:27:03] [0.3.78] [INFO] 172.17.0.1:51664 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:27:03] [0.3.78] [INFO] 172.17.0.1:51656 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:27:03] [0.3.78] [INFO] 172.17.0.1:51664 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:27:03] [0.3.78] [INFO] 172.17.0.1:51656 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[2025-04-26 00:27:03] [0.3.78] [INFO] xiaomusic.py:430: try get_filename. filename:music/Kalafina - oblivious.mp3


[2025-04-26 00:27:03] [0.3.78] [INFO] xiaomusic.py:536: get_music_url local music. name:Kalafina - oblivious, filename:Kalafina - oblivious.mp3


[00:27:03] [0.3.78] [INFO] 172.17.0.1:51664 - "GET /musicinfo?name=Kalafina%20-%20oblivious&musictag=true HTTP/1.1" 200


[00:27:03] [0.3.78] [INFO] 172.17.0.1:51676 - "GET /playingmusic?did=725574603 HTTP/1.1" 200


[00:27:03] [0.3.78] [INFO] 172.17.0.1:51664 - "GET /playingmusic?did=725574603 HTTP/1.1" 200`

---

### 评论 103 - hanxi

> 作者你好，我出现了上述“本机有声音，小爱音箱没声音，请问什么原因？”这个问题，按顺序排查，默认测试链接正常，容器日志中音乐链接也正常播放，但测试链接中如果填写我的容器日志中音乐链接，也就是本地音源就无法播放，换了十几个mp3音源，均无法正常播放，小爱总说网络错误 `[2025-04-26 00:24:09] [0.3.78] [INFO] analytics.py:105: umami Status: 400
> 
> [00:25:03] [0.3.78] [INFO] 172.17.0.1:48048 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:25:03] [0.3.78] [INFO] 172.17.0.1:48054 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:25:03] [0.3.78] [INFO] 172.17.0.1:48048 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:25:03] [0.3.78] [INFO] 172.17.0.1:48054 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:25:03] [0.3.78] [INFO] 172.17.0.1:48048 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [2025-04-26 00:25:03] [0.3.78] [INFO] xiaomusic.py:430: try get_filename. filename:music/Kalafina - oblivious.mp3
> 
> [2025-04-26 00:25:03] [0.3.78] [INFO] xiaomusic.py:536: get_music_url local music. name:Kalafina - oblivious, filename:Kalafina - oblivious.mp3
> 
> [00:25:03] [0.3.78] [INFO] 172.17.0.1:48064 - "GET /musicinfo?name=Kalafina%20-%20oblivious&musictag=true HTTP/1.1" 200
> 
> [00:25:03] [0.3.78] [INFO] 172.17.0.1:48054 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:25:03] [0.3.78] [INFO] 172.17.0.1:48064 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:26:03] [0.3.78] [INFO] 172.17.0.1:39316 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:26:03] [0.3.78] [INFO] 172.17.0.1:39332 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:26:03] [0.3.78] [INFO] 172.17.0.1:39334 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:26:03] [0.3.78] [INFO] 172.17.0.1:39316 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [2025-04-26 00:26:03] [0.3.78] [INFO] xiaomusic.py:430: try get_filename. filename:music/Kalafina - oblivious.mp3
> 
> [2025-04-26 00:26:03] [0.3.78] [INFO] xiaomusic.py:536: get_music_url local music. name:Kalafina - oblivious, filename:Kalafina - oblivious.mp3
> 
> [00:26:03] [0.3.78] [INFO] 172.17.0.1:39332 - "GET /musicinfo?name=Kalafina%20-%20oblivious&musictag=true HTTP/1.1" 200
> 
> [00:26:03] [0.3.78] [INFO] 172.17.0.1:39354 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:26:03] [0.3.78] [INFO] 172.17.0.1:39344 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:26:03] [0.3.78] [INFO] 172.17.0.1:39370 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:27:03] [0.3.78] [INFO] 172.17.0.1:51656 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:27:03] [0.3.78] [INFO] 172.17.0.1:51664 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:27:03] [0.3.78] [INFO] 172.17.0.1:51656 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:27:03] [0.3.78] [INFO] 172.17.0.1:51664 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:27:03] [0.3.78] [INFO] 172.17.0.1:51656 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [2025-04-26 00:27:03] [0.3.78] [INFO] xiaomusic.py:430: try get_filename. filename:music/Kalafina - oblivious.mp3
> 
> [2025-04-26 00:27:03] [0.3.78] [INFO] xiaomusic.py:536: get_music_url local music. name:Kalafina - oblivious, filename:Kalafina - oblivious.mp3
> 
> [00:27:03] [0.3.78] [INFO] 172.17.0.1:51664 - "GET /musicinfo?name=Kalafina%20-%20oblivious&musictag=true HTTP/1.1" 200
> 
> [00:27:03] [0.3.78] [INFO] 172.17.0.1:51676 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> 
> [00:27:03] [0.3.78] [INFO] 172.17.0.1:51664 - "GET /playingmusic?did=725574603 HTTP/1.1" 200`

你部署在哪里的？是不是防火墙的问题？

---

### 评论 104 - KagurazakaAsahi

> > 作者你好，我出现了上述“本机有声音，小爱音箱没声音，请问什么原因？”这个问题，按顺序排查，默认测试链接正常，容器日志中音乐链接也正常播放，但测试链接中如果填写我的容器日志中音乐链接，也就是本地音源就无法播放，换了十几个mp3音源，均无法正常播放，小爱总说网络错误 `[2025-04-26 00:24:09] [0.3.78] [INFO] analytics.py:105: umami Status: 400
> > [00:25:03] [0.3.78] [INFO] 172.17.0.1:48048 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:25:03] [0.3.78] [INFO] 172.17.0.1:48054 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:25:03] [0.3.78] [INFO] 172.17.0.1:48048 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:25:03] [0.3.78] [INFO] 172.17.0.1:48054 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:25:03] [0.3.78] [INFO] 172.17.0.1:48048 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [2025-04-26 00:25:03] [0.3.78] [INFO] xiaomusic.py:430: try get_filename. filename:music/Kalafina - oblivious.mp3
> > [2025-04-26 00:25:03] [0.3.78] [INFO] xiaomusic.py:536: get_music_url local music. name:Kalafina - oblivious, filename:Kalafina - oblivious.mp3
> > [00:25:03] [0.3.78] [INFO] 172.17.0.1:48064 - "GET /musicinfo?name=Kalafina%20-%20oblivious&musictag=true HTTP/1.1" 200
> > [00:25:03] [0.3.78] [INFO] 172.17.0.1:48054 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:25:03] [0.3.78] [INFO] 172.17.0.1:48064 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:26:03] [0.3.78] [INFO] 172.17.0.1:39316 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:26:03] [0.3.78] [INFO] 172.17.0.1:39332 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:26:03] [0.3.78] [INFO] 172.17.0.1:39334 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:26:03] [0.3.78] [INFO] 172.17.0.1:39316 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [2025-04-26 00:26:03] [0.3.78] [INFO] xiaomusic.py:430: try get_filename. filename:music/Kalafina - oblivious.mp3
> > [2025-04-26 00:26:03] [0.3.78] [INFO] xiaomusic.py:536: get_music_url local music. name:Kalafina - oblivious, filename:Kalafina - oblivious.mp3
> > [00:26:03] [0.3.78] [INFO] 172.17.0.1:39332 - "GET /musicinfo?name=Kalafina%20-%20oblivious&musictag=true HTTP/1.1" 200
> > [00:26:03] [0.3.78] [INFO] 172.17.0.1:39354 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:26:03] [0.3.78] [INFO] 172.17.0.1:39344 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:26:03] [0.3.78] [INFO] 172.17.0.1:39370 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:27:03] [0.3.78] [INFO] 172.17.0.1:51656 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:27:03] [0.3.78] [INFO] 172.17.0.1:51664 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:27:03] [0.3.78] [INFO] 172.17.0.1:51656 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:27:03] [0.3.78] [INFO] 172.17.0.1:51664 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:27:03] [0.3.78] [INFO] 172.17.0.1:51656 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [2025-04-26 00:27:03] [0.3.78] [INFO] xiaomusic.py:430: try get_filename. filename:music/Kalafina - oblivious.mp3
> > [2025-04-26 00:27:03] [0.3.78] [INFO] xiaomusic.py:536: get_music_url local music. name:Kalafina - oblivious, filename:Kalafina - oblivious.mp3
> > [00:27:03] [0.3.78] [INFO] 172.17.0.1:51664 - "GET /musicinfo?name=Kalafina%20-%20oblivious&musictag=true HTTP/1.1" 200
> > [00:27:03] [0.3.78] [INFO] 172.17.0.1:51676 - "GET /playingmusic?did=725574603 HTTP/1.1" 200
> > [00:27:03] [0.3.78] [INFO] 172.17.0.1:51664 - "GET /playingmusic?did=725574603 HTTP/1.1" 200`
> 
> 你部署在哪里的？是不是防火墙的问题？

部署在我台式机上，关闭了防火墙，台式机与小爱音箱连接了同一个热点

---

### 评论 105 - ocean412257013

请教  在build的时候报错，是哪个意思

![Image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/8e6df9f7-0dd1-4bea-b584-c271073a7411)

---

### 评论 106 - hanxi

> 请教 在build的时候报错，是哪个意思
> 
> ![Image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/8e6df9f7-0dd1-4bea-b584-c271073a7411)

换个国内的镜像。

---

### 评论 107 - quickflash2

我用DOCKER布署的，语音可以正常播放本地音乐，但是说“本地搜索播放”始终无法执行，都是小爱本身的的语音提示，网页里的搜索和下载是正常使用的，想问下怎么解决

---

### 评论 108 - hanxi

> 我用DOCKER布署的，语音可以正常播放本地音乐，但是说“本地搜索播放”始终无法执行，都是小爱本身的的语音提示，网页里的搜索和下载是正常使用的，想问下怎么解决

播放歌曲口令是否正常？

---

### 评论 109 - quickflash2

> > 我用DOCKER布署的，语音可以正常播放本地音乐，但是说“本地搜索播放”始终无法执行，都是小爱本身的的语音提示，网页里的搜索和下载是正常使用的，想问下怎么解决
> 
> 播放歌曲口令是否正常？

播放口令正常，使用网页版搜索功能正常，就是“本地搜索播放”和“小爱搜索播放”语音不能使用

---

### 评论 110 - wtyeoh

我在设置里勾选了两台小爱音箱，叫任意一台播放的时候两台都会一起播放，是不是只能勾选一个？

---

### 评论 111 - quickflash2

> > 我用DOCKER布署的，语音可以正常播放本地音乐，但是说“本地搜索播放”始终无法执行，都是小爱本身的的语音提示，网页里的搜索和下载是正常使用的，想问下怎么解决
> 
> 播放歌曲口令是否正常？

楼主，我问题解决了，命令调用里不知道怎么回事这个搜索播放的命令没有了，重新加上就可以了，谢谢大佬

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/99)
