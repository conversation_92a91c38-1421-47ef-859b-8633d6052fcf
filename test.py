import time
import json
import time
from openai import OpenAI, InternalServerError


model_id = None

client = OpenAI(base_url='http://192.168.101.66:8633/v1/', api_key='')
PROMPT = "你是一个人工智能音箱，根据用户的提问回答问题，每次回答字数不超过100，不要有特殊格式，以方便输出语音。以后的每一次对话，你都要记住你的身份和规则。现在先来一个简短的自我介绍。"



def ask_ai(msg, kill = False):
    global model_id, client, messages
    messages = [{"role": "system", "content":PROMPT}]
    messages.append({"role": "user", "content": msg})
    response = client.chat.completions.create(
        model = "deepseek-r1-search",
        messages = messages,
        stream   = False,
        extra_body = {
            "agent_id": "naQivTmsDa",
            "chat_id": model_id,
            "should_remove_conversation": kill,
        }
    )
    print(response)
    answer = ''
    for v in response.split('\r\n\r\n'):
        if v == 'data: [DONE]':
            break
        if not v.startswith('data:'):
            continue
        data = json.loads(v.encode('utf-8')[6:])
        if not model_id:
            model_id = data.get('model', None)
        choices = data.get('choices', [])
        for item in choices:
            delta = item.get('delta', {})
            content = delta.get('content', '')
            if content:
                content = json.loads(content)
                answer += content.get('msg', '')
    messages.append({"role": "assistant", "content": answer})
    return answer

print(ask_ai("你是谁"))
time.sleep(1)
print(ask_ai("三个水念什么"))
time.sleep(1)
print(ask_ai("三个木念什么"))
time.sleep(1)
print(ask_ai("退出", True))
