---
title: 如何批量下载歌曲
---

# 如何批量下载歌曲

批量下载歌曲依赖的是 yt-dlp 批量下载播放列表里的视频并转为 mp3 实现的。

先进入到歌曲下载工具页面:

> 默认主题 => 设置 => 歌曲下载工具

![Screenshot_2024-09-29-22-36-12-178_com android chrome-edit](https://gproxy.hanxi.cc/proxy/user-attachments/assets/ddd2af00-cd9e-4938-9450-56503453807c)


已经测试过 B 站和 youtube 两种播放列表，播放列表的链接是有要求，不能有其他多余参数。

比如 B 站的是这样的

https://m.bilibili.com/video/BV1WUsDezE88

youtube 的是这样的

https://m.youtube.com/playlist?list=PLUD2d-pqyvT6_ztf31hx-5SsUUvY5UsQn

输入歌单名字是用于保存的文件夹名字，最好不是已经存在的名字，每次下载歌单都取个新名字比较合适。

已知 youtube 需要上传无痕模式下的 cookies.txt 文件才能正常下载。具体步骤见 /issues/210.html 。


也支持单独下载一个链接只有一首歌曲的。

## 评论


### 评论 1 - lazybabyz

 默认主题 => 设置 =>没有显示找到 歌曲下载工具,
有一个 歌单地址 歌单内容: 输入B站测试地址显示返回无效

![aaa](https://gproxy.hanxi.cc/proxy/user-attachments/assets/31e224cb-fcbd-4841-b545-bfbd2496061b)


---

### 评论 2 - hanxi

等0.3.38版本。

---

### 评论 3 - hzqgogogo

![Image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/6751adf0-fea6-40b9-8d4b-48696a3100b3)下载音乐报错

---

### 评论 4 - hanxi

> ![Image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/6751adf0-fea6-40b9-8d4b-48696a3100b3)下载音乐报错

下载b站歌曲不用配proxy

---

### 评论 5 - MakiseKurisu

是必须要下载下来么？那有没有什么方法限定最大占用的磁盘空间？文档没有网页接口的介绍不太清楚具体有哪些功能

---

### 评论 6 - hanxi

> 是必须要下载下来么？那有没有什么方法限定最大占用的磁盘空间？文档没有网页接口的介绍不太清楚具体有哪些功能

没有这功能。

---

### 评论 7 - sagadc

請問一下YT下載失敗，因為我是在台灣不需要加proxy
看了一下log也看不出原因來

yt-dlp應該不用在自己額外安裝吧？

[yt-dl.txt](https://github.com/user-attachments/files/19379757/yt-dl.txt)

---

### 评论 8 - hanxi

> 請問一下YT下載失敗，因為我是在台灣不需要加proxy 看了一下log也看不出原因來
> 
> yt-dlp應該不用在自己額外安裝吧？
> 
> [yt-dl.txt](https://github.com/user-attachments/files/19379757/yt-dl.txt)

你打开调试模式再复现一下，可能需要上传 cookie 才能下载 yt 的。

---

### 评论 9 - sagadc

> > 請問一下YT下載失敗，因為我是在台灣不需要加proxy 看了一下log也看不出原因來
> > yt-dlp應該不用在自己額外安裝吧？
> > [yt-dl.txt](https://github.com/user-attachments/files/19379757/yt-dl.txt)
> 
> 你打开调试模式再复现一下，可能需要上传 cookie 才能下载 yt 的。

感謝，看來要安裝那邊要說明一下要開啟yt-dlp下載最好是上傳cookie比較保險

---

### 评论 10 - nfzsh

看日志好像只获取到了第一首？

---

### 评论 11 - hzqgogogo

已收到你的邮件！

---

### 评论 12 - nfzsh

> 已收到你的邮件！

哦我知道了，你这个是针对分P的，我这个是个合集，我看看哪天我提个PR吧

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/212)
