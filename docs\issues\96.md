---
title: ios系统上的捷径配置
---

# ios系统上的捷径配置

下面是播放音乐和关机两个示例。只要在 web 页面上能看到的功能，都有对应的 http 请求接口，都可以用来配置捷径。

![mmexport1719767452647](https://gproxy.hanxi.cc/proxy/hanxi/xiaomusic/assets/1185757/db0a1856-e1ed-47cb-972d-d997f71bf92b)

![mmexport1719767449742](https://gproxy.hanxi.cc/proxy/hanxi/xiaomusic/assets/1185757/92b7bc4b-9699-49cc-956a-4bddb6bd50fa)


## 评论


### 评论 1 - Yumega

为什么我的捷径设置无效
http://192.168.11.1:5678/cmd 这个电脑可以打开 显示 {"detail":"Method Not Allowed"}

参照楼主的方法 用iOS捷径无效

---

### 评论 2 - hanxi

方便截图看看怎么填的吗？

---

### 评论 3 - Yumega

![Image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/448e0837-665a-4c46-9598-9c747bdbc422)

---

### 评论 4 - Yumega

> 方便截图看看怎么填的吗？

😏什么原因

---

### 评论 5 - hanxi

@Yumega 等有空我试试，目前没时间。

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/96)
