---
title: docker compose 命令行安装教程
---

# docker compose 命令行安装教程

本教程针对于有命令行环境，且已经安装好 docker compose 的用户。步骤超级简单，基本只要复制粘贴就能跑起来。如果需要修改路径或者端口，可以复制到编辑器，修改后再复制粘贴到命令行执行。编辑时注意不要修改到文件格式，包括缩进。

## 创建 docker-compose.yml 文件

假设 `docker-compose.yml` 文件的存放到路径为 `/xiaomusic/docker-compose.yml` 。执行下面的命令即可：

```shell
mkdir -p /xiaomusic
cat <<EOF > /xiaomusic/docker-compose.yml
services:
  xiaomusic:
    image: docker.hanxi.cc/hanxi/xiaomusic
    container_name: xiaomusic
    restart: unless-stopped
    ports:
      - 58090:8090
    environment:
      XIAOMUSIC_PUBLIC_PORT: 58090
    volumes:
      - /xiaomusic_conf:/app/conf
      - /xiaomusic_music:/app/music
EOF
```

- `/xiaomusic_conf` 为配置文件存放目录，一般不需要修改。
- `/xiaomusic_music` 为音乐存放目录，你可以替换为自己想要存放的目录，注意填绝对路径，在 Linux 下是 `/` 开头的，在 Windows 下是盘符开头，比如： `D:/music`。
- 端口 8090 不要修改，是容器内的端口。
- 端口 58090 可以修改，如果想要修改，两个 58090 都需要同时修改，这个端口是访问 web 后台的端口。

## 启动

```shell
cd /xiaomusic
docker compose up -d
```

启动后，就能使用 <http://nasip:58090> 来访问 web 后台了，把 nasip 替换成你的 nas 的 IP 。

## 后台设置

![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/424c45af-6937-4fee-bb7d-855973ef0d5d)

填好账号密码，和自动填 IP 和端口，然后滚动到页面最下面，点击保存按钮。然后刷新设置页面，再勾选小爱音箱，再保存即可。

![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/c63c3ff1-3a80-4f47-971e-761eb9187ae0)


## 更新

想要更新镜像，只需要复制粘贴下面的命令就行，注意 `/xiaomusic` 目录是 `docker-compose.yml` 文件所在的目录。

```shell
cd /xiaomusic
docker compose pull
docker compose up -d
```

## 关闭

```shell
cd /xiaomusic
docker compose down
```

## 评论


### 评论 1 - tiger326

我用这个是可以正常启动和搭建好xiaomusic, 一切都OK.
不过我的NAS是qnap, 已禁用默认的admin用户, 使用自建的管理员账户登录.
但这个配置会默认使用admin用户执行, 导致一旦产生tmp和download目录以及相关文件, 都归属于admin用户.
结果我自建的用户在NAS上无法删除和编辑这些下载的文件.

xiaomusic是否支持创建时指定用户和用户组? 

---

### 评论 2 - hanxi

@tiger326 把下面的 username 换成普通的用户名即可，换成 uid 数字也行，一般是 1000 

```shell
mkdir -p /xiaomusic
cat <<EOF > /xiaomusic/docker-compose.yml
services:
  xiaomusic:
    image: docker.hanxi.cc/hanxi/xiaomusic
    container_name: xiaomusic
    restart: unless-stopped
    user: username
    ports:
      - 58090:8090
    environment:
      XIAOMUSIC_PUBLIC_PORT: 58090
    volumes:
      - /xiaomusic_conf:/app/conf
      - /xiaomusic_music:/app/music
EOF
```

---

### 评论 3 - worrywast

为啥我的播放完一首音乐之后，还会播放该歌曲开头一点点才会放下一首呢？

---

### 评论 4 - hanxi

> 为啥我的播放完一首音乐之后，还会播放该歌曲开头一点点才会放下一首呢？

正常现象。可以把延迟设置为0试试。


---

### 评论 5 - worrywast

> > 为啥我的播放完一首音乐之后，还会播放该歌曲开头一点点才会放下一首呢？
> 
> 正常现象。可以把延迟设置为0试试。

是这个选项吗：“下一首歌延迟播放秒数:”
我设置为0，也会有这个现象，只是没有之前播放开头那么多

---

### 评论 6 - hanxi

@worrywast 等下个版本我优化一下，允许设置成负数吧。

---

### 评论 7 - worrywast

> [@worrywast](https://github.com/worrywast) 等下个版本我优化一下，允许设置成负数吧。

辛苦大佬~

---

### 评论 8 - hanxi

@worrywast 现在就是可以填负数的，你可以试试看。

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/360)
