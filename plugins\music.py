import os
import time
import random
import asyncio

from . import Plugin

from xiaomusic.const import (
    COOKIE_TEMPLATE,
    GET_ASK_BY_MINA,
    LATEST_ASK_API,
    NEED_USE_PLAY_MUSIC_API,
    PLAY_TYPE_ALL,
    PLAY_TYPE_ONE,
    PLAY_TYPE_RND,
    PLAY_TYPE_SEQ,
    PLAY_TYPE_SIN,
    SUPPORT_MUSIC_TYPE,
    TTS_COMMAND,
)

from xiaomusic.utils import (
    Metadata,
    chinese_to_number,
    chmodfile,
    custom_sort_key,
    deepcopy_data_no_sensitive_info,
    extract_audio_metadata,
    find_best_match,
    fuzzyfinder,
    get_local_music_duration,
    get_web_music_duration,
    list2str,
    not_in_dirs,
    parse_cookie_string,
    parse_str_to_dict,
    save_picture_by_base64,
    set_music_tag_to_file,
    thdplay,
    traverse_music_directory,
    try_add_access_control_param,
)

class Music(Plugin):
    KEYWORDS = {
        "播放歌曲": "play",
        "播放本地歌曲": "playlocal",
        "关机": "stop",
        "下一首": "play_next",
        "单曲循环": "set_play_type_one",
        "全部循环": "set_play_type_all",
        "随机播放": "set_random_play",
        "分钟后关机": "stop_after_minute",
        "播放列表": "play_music_list",
        "刷新列表": "gen_music_list",
        "本地播放歌曲": "playlocal",
        "放歌曲": "play",
        "暂停": "stop",
        "停止": "stop",
        "停止播放": "stop",
        "音量": "set_myvolume",
        "继续": "stop",
    }

    async def check_full_match_cmd(self, did: str, msg: str, ctrl_panel: bool = False) -> bool:
        opt = Music.KEYWORDS.get(msg, None)
        if not opt:
            return False

    async def check_pattern_match_cmd(self, did: str, msg: str, ctrl_panel: bool = False) -> bool:
        return False


    def find_real_music_name(self, name, n):
        if not self.config.enable_fuzzy_match:
            self.log.debug("没开启模糊匹配")
            return []

        all_music_list = list(self.all_music.keys())
        real_names = find_best_match(
            name,
            all_music_list,
            cutoff=self.config.fuzzy_match_cutoff,
            n=n,
            extra_search_index=self._extra_index_search,
        )
        if real_names:
            if n > 1 and name not in real_names:
                # 模糊匹配模式，扩大范围再找，最后保留随机 n 个
                real_names = find_best_match(
                    name,
                    all_music_list,
                    cutoff=self.config.fuzzy_match_cutoff,
                    n=n * 2,
                    extra_search_index=self._extra_index_search,
                )
                random.shuffle(real_names)
                real_names = real_names[:n]
            elif name in real_names:
                # 可以精确匹配，限制只返回一个（保证网页端播放可用）
                real_names = [name]
            self.log.info(f"根据【{name}】找到歌曲【{real_names}】")
            return real_names
        self.log.info(f"没找到歌曲【{name}】")
        return []


    # 播放歌曲
    async def play(self, name="", search_key="", exact=True, update_cur_list=False):
        self._last_cmd = "play"
        return await self._play(
            name=name,
            search_key=search_key,
            exact=exact,
            update_cur_list=update_cur_list,
        )

    async def _play(self, name="", search_key="", exact=True, update_cur_list=False):
        if search_key == "" and name == "":
            if self.check_play_next():
                await self._play_next()
                return
            else:
                name = self.get_cur_music()
        self.log.info(f"play. search_key:{search_key} name:{name}")

        # 本地歌曲不存在时下载
        if exact:
            names = self.xiaomusic.find_real_music_name(name, n=1)
        else:
            names = self.xiaomusic.find_real_music_name(
                name, n=self.config.search_music_count
            )
        if len(names) > 0:
            if not exact:
                if len(names) > 1:  # 大于一首歌才更新
                    self._play_list = names
                    self.device.cur_playlist = "临时搜索列表"
                    self.update_playlist()
                else:  # 只有一首歌，append
                    self._play_list = self._play_list + names
                    self.device.cur_playlist = "临时搜索列表"
                    self.update_playlist(reorder=False)
            name = names[0]
            if update_cur_list and (name not in self._play_list):
                # 根据当前歌曲匹配歌曲列表
                self.device.cur_playlist = self.find_cur_playlist(name)
                self.update_playlist()
            self.log.debug(
                f"当前播放列表为：{list2str(self._play_list, self.config.verbose)}"
            )
        elif not self.xiaomusic.is_music_exist(name):
            if self.config.disable_download:
                await self.do_tts(f"本地不存在歌曲{name}")
                return
            await self.download(search_key, name)
            # 把文件插入到播放列表里
            await self.add_download_music(name)
        await self._playmusic(name)

    # 下一首
    async def play_next(self):
        return await self._play_next()

    async def _play_next(self):
        self.log.info("开始播放下一首")
        name = self.get_cur_music()
        if (
            self.device.play_type == PLAY_TYPE_ALL
            or self.device.play_type == PLAY_TYPE_RND
            or self.device.play_type == PLAY_TYPE_SEQ
            or name == ""
            or (
                (name not in self._play_list) and self.device.play_type != PLAY_TYPE_ONE
            )
        ):
            name = self.get_next_music()
        self.log.info(f"_play_next. name:{name}, cur_music:{self.get_cur_music()}")
        if name == "":
            await self.do_tts("本地没有歌曲")
            return
        await self._play(name, exact=True)

    # 上一首
    async def play_prev(self):
        return await self._play_prev()

    async def _play_prev(self):
        self.log.info("开始播放上一首")
        name = self.get_cur_music()
        if (
            self.device.play_type == PLAY_TYPE_ALL
            or self.device.play_type == PLAY_TYPE_RND
            or name == ""
            or (name not in self._play_list)
        ):
            name = self.get_prev_music()
        self.log.info(f"_play_prev. name:{name}, cur_music:{self.get_cur_music()}")
        if name == "":
            await self.do_tts("本地没有歌曲")
            return
        await self._play(name, exact=True)

    # 播放本地歌曲
    async def playlocal(self, name, exact=True, update_cur_list=False):
        self._last_cmd = "playlocal"
        if name == "":
            if self.check_play_next():
                await self._play_next()
                return
            else:
                name = self.get_cur_music()

        self.log.info(f"playlocal. name:{name}")

        # 本地歌曲不存在时下载
        if exact:
            names = self.xiaomusic.find_real_music_name(name, n=1)
        else:
            names = self.xiaomusic.find_real_music_name(
                name, n=self.config.search_music_count
            )
        if len(names) > 0:
            if not exact:
                if len(names) > 1:  # 大于一首歌才更新
                    self._play_list = names
                    self.device.cur_playlist = "临时搜索列表"
                    self.update_playlist()
                else:  # 只有一首歌，append
                    self._play_list = self._play_list + names
                    self.device.cur_playlist = "临时搜索列表"
                    self.update_playlist(reorder=False)
            name = names[0]
            if update_cur_list:
                # 根据当前歌曲匹配歌曲列表
                self.device.cur_playlist = self.find_cur_playlist(name)
                self.update_playlist()
            self.log.debug(
                f"当前播放列表为：{list2str(self._play_list, self.config.verbose)}"
            )
        elif not self.xiaomusic.is_music_exist(name):
            await self.do_tts(f"本地不存在歌曲{name}")
            return
        await self._playmusic(name)

    async def _playmusic(self, name):
        # 取消组内所有的下一首歌曲的定时器
        self.cancel_group_next_timer()

        self._playing = True
        self.device.cur_music = name

        self.log.info(f"cur_music {self.get_cur_music()}")
        sec, url = await self.xiaomusic.get_music_sec_url(name)
        await self.group_force_stop_xiaoai()
        self.log.info(f"播放 {url}")
        # 有3方设备打开 /static/3thplay.html 通过socketio连接返回true 忽律小爱音箱的播放
        online = await thdplay("play", url, self.xiaomusic.thdtarget)
        if not online:
            results = await self.group_player_play(url, name)
            if all(ele is None for ele in results):
                self.log.info(f"播放 {name} 失败. 失败次数: {self._play_failed_cnt}")
                await asyncio.sleep(1)
                if (
                    self.isplaying()
                    and self._last_cmd != "stop"
                    and self._play_failed_cnt < 10
                ):
                    self._play_failed_cnt = self._play_failed_cnt + 1
                    await self._play_next()
                return
        # 重置播放失败次数
        self._play_failed_cnt = 0

        self.log.info(f"【{name}】已经开始播放了")
        await self.xiaomusic.analytics.send_play_event(name, sec, self.hardware)

        # 设置下一首歌曲的播放定时器
        if sec <= 1:
            self.log.info(f"【{name}】不会设置下一首歌的定时器")
            return
        sec = sec + self.config.delay_sec
        self._start_time = time.time()
        self._duration = sec
        self._paused_time = 0
        await self.set_next_music_timeout(sec)
        self.xiaomusic.save_cur_config()


    # 是否在下载中
    def isdownloading(self):
        if not self._download_proc:
            return False

        if self._download_proc.returncode is not None:
            self.log.info(
                f"Process exited with returncode:{self._download_proc.returncode}"
            )
            return False

        self.log.info("Download Process is still running.")
        return True

    # 下载歌曲
    async def download(self, search_key, name):
        if self._download_proc:
            try:
                self._download_proc.kill()
            except ProcessLookupError:
                pass

        sbp_args = (
            "yt-dlp",
            f"{self.config.search_prefix}{search_key}",
            "-x",
            "--audio-format",
            "mp3",
            "--audio-quality",
            "0",
            "--paths",
            self.download_path,
            "-o",
            f"{name}.mp3",
            "--ffmpeg-location",
            f"{self.ffmpeg_location}",
            "--no-playlist",
        )

        if self.config.proxy:
            sbp_args += ("--proxy", f"{self.config.proxy}")

        if self.config.enable_yt_dlp_cookies:
            sbp_args += ("--cookies", f"{self.config.yt_dlp_cookies_path}")

        if self.config.loudnorm:
            sbp_args += ("--postprocessor-args", f"-af {self.config.loudnorm}")

        cmd = " ".join(sbp_args)
        self.log.info(f"download cmd: {cmd}")
        self._download_proc = await asyncio.create_subprocess_exec(*sbp_args)
        await self.do_tts(f"正在下载歌曲{search_key}")
        self.log.info(f"正在下载中 {search_key} {name}")
        await self._download_proc.wait()
        # 下载完成后，修改文件权限
        file_path = os.path.join(self.download_path, f"{name}.mp3")
        chmodfile(file_path)

    # 继续播放被打断的歌曲
    async def check_replay(self):
        if self.isplaying() and not self.isdownloading():
            if not self.config.continue_play:
                # 重新播放歌曲
                self.log.info("现在重新播放歌曲")
                await self._play()
            else:
                self.log.info(
                    f"继续播放歌曲. self.config.continue_play:{self.config.continue_play}"
                )
        else:
            self.log.info(
                f"不会继续播放歌曲. isplaying:{self.isplaying()} isdownloading:{self.isdownloading()}"
            )

    # 当前是否正在播放歌曲
    def isplaying(self):
        return self._playing

    # 把下载的音乐加入播放列表
    async def add_download_music(self, name):
        filepath = os.path.join(self.download_path, f"{name}.mp3")
        self.xiaomusic.all_music[name] = filepath
        # 应该很快，阻塞运行
        await self.xiaomusic._gen_all_music_tag({name: filepath})
        if name not in self._play_list:
            self._play_list.append(name)
            self.log.info(f"add_download_music add_music {name}")
            self.log.debug(self._play_list)

    def get_music(self, direction="next"):
        play_list_len = len(self._play_list)
        if play_list_len == 0:
            self.log.warning("当前播放列表没有歌曲")
            return ""
        index = 0
        try:
            index = self._play_list.index(self.get_cur_music())
        except ValueError:
            pass

        if play_list_len == 1:
            new_index = index  # 当只有一首歌曲时保持当前索引不变
        else:
            if direction == "next":
                new_index = index + 1
                if (
                    self.device.play_type == PLAY_TYPE_SEQ
                    and new_index >= play_list_len
                ):
                    self.log.info("顺序播放结束")
                    return ""
                if new_index >= play_list_len:
                    new_index = 0
            elif direction == "prev":
                new_index = index - 1
                if new_index < 0:
                    new_index = play_list_len - 1
            else:
                self.log.error("无效的方向参数")
                return ""

        name = self._play_list[new_index]
        if not self.xiaomusic.is_music_exist(name):
            self._play_list.pop(new_index)
            self.log.info(f"pop not exist music: {name}")
            return self.get_music(direction)
        return name

    # 获取下一首
    def get_next_music(self):
        return self.get_music(direction="next")

    # 获取上一首
    def get_prev_music(self):
        return self.get_music(direction="prev")

    # 判断是否播放下一首歌曲
    def check_play_next(self):
        # 当前歌曲不在当前播放列表
        if self.get_cur_music() not in self._play_list:
            self.log.info(f"当前歌曲 {self.get_cur_music()} 不在当前播放列表")
            return True

        # 当前没我在播放的歌曲
        if self.get_cur_music() == "":
            self.log.info("当前没我在播放的歌曲")
            return True
        else:
            # 当前播放的歌曲不存在了
            if not self.xiaomusic.is_music_exist(self.get_cur_music()):
                self.log.info(f"当前播放的歌曲 {self.get_cur_music()} 不存在了")
                return True
        return False

    # 同一组设备播放
    async def group_player_play(self, url, name=""):
        device_id_list = self.xiaomusic.get_group_device_id_list(self.group_name)
        tasks = [
            self.play_one_url(device_id, url, name) for device_id in device_id_list
        ]
        results = await asyncio.gather(*tasks)
        self.log.info(f"group_player_play {url} {device_id_list} {results}")
        return results

    async def play_one_url(self, device_id, url, name):
        ret = None
        try:
            audio_id = await self._get_audio_id(name)
            if self.config.continue_play:
                ret = await self.xiaomusic.mina_service.play_by_music_url(
                    device_id, url, _type=1, audio_id=audio_id
                )
                self.log.info(
                    f"play_one_url continue_play device_id:{device_id} ret:{ret} url:{url} audio_id:{audio_id}"
                )
            elif self.config.use_music_api or (
                self.hardware in NEED_USE_PLAY_MUSIC_API
            ):
                ret = await self.xiaomusic.mina_service.play_by_music_url(
                    device_id, url, audio_id=audio_id
                )
                self.log.info(
                    f"play_one_url play_by_music_url device_id:{device_id} ret:{ret} url:{url} audio_id:{audio_id}"
                )
            else:
                ret = await self.xiaomusic.mina_service.play_by_url(device_id, url)
                self.log.info(
                    f"play_one_url play_by_url device_id:{device_id} ret:{ret} url:{url}"
                )
        except Exception as e:
            self.log.exception(f"Execption {e}")
        return ret

    async def _get_audio_id(self, name):
        audio_id = self.config.use_music_audio_id or "1582971365183456177"
        if not (self.config.use_music_api or self.config.continue_play):
            return str(audio_id)
        try:
            params = {
                "query": name,
                "queryType": 1,
                "offset": 0,
                "count": 6,
                "timestamp": int(time.time_ns() / 1000),
            }
            response = await self.xiaomusic.mina_service.mina_request(
                "/music/search", params
            )
            for song in response["data"]["songList"]:
                if song["originName"] == "QQ音乐":
                    audio_id = song["audioID"]
                    break
            # 没找到QQ音乐的歌曲，取第一个
            if audio_id == 1582971365183456177:
                audio_id = response["data"]["songList"][0]["audioID"]
            self.log.debug(f"_get_audio_id. name: {name} songId:{audio_id}")
        except Exception as e:
            self.log.error(f"_get_audio_id {e}")
        return str(audio_id)

    # 重置计时器
    async def reset_timer_when_answer(self, answer_length):
        if not (self.isplaying() and self.config.continue_play):
            return
        pause_time = answer_length / 5 + 1
        offset, duration = self.get_offset_duration()
        self._paused_time += pause_time
        new_time = duration - offset + pause_time
        await self.set_next_music_timeout(new_time)
        self.log.info(
            f"reset_timer 延长定时器. answer_length:{answer_length} pause_time:{pause_time}"
        )

    # 设置下一首歌曲的播放定时器
    async def set_next_music_timeout(self, sec):
        self.cancel_next_timer()

        async def _do_next():
            await asyncio.sleep(sec)
            try:
                self.log.info("定时器时间到了")
                if self._next_timer:
                    self._next_timer = None
                    if self.device.play_type == PLAY_TYPE_SIN:
                        self.log.info("单曲播放不继续播放下一首")
                        await self.stop(arg1="notts")
                    else:
                        await self._play_next()
                else:
                    self.log.info("定时器时间到了但是不见了")

            except Exception as e:
                self.log.error(f"Execption {e}")

        self._next_timer = asyncio.create_task(_do_next())
        self.log.info(f"{sec} 秒后将会播放下一首歌曲")


    async def set_play_type(self, play_type, dotts=True):
        self.device.play_type = play_type
        self.xiaomusic.save_cur_config()
        if dotts:
            tts = self.config.get_play_type_tts(play_type)
            await self.do_tts(tts)
        self.update_playlist()

    async def play_music_list(self, list_name, music_name):
        self._last_cmd = "play_music_list"
        self.device.cur_playlist = list_name
        self.update_playlist()
        self.log.info(f"开始播放列表{list_name} {music_name}")
        await self._play(music_name, exact=True)

    async def stop(self, arg1=""):
        self._last_cmd = "stop"
        self._playing = False
        if arg1 != "notts":
            await self.do_tts(self.config.stop_tts_msg)
        await asyncio.sleep(3)  # 等它说完
        # 取消组内所有的下一首歌曲的定时器
        await thdplay("stop", "", self.xiaomusic.thdtarget)
        self.cancel_group_next_timer()
        await self.group_force_stop_xiaoai()
        self.log.info("stop now")

    async def group_force_stop_xiaoai(self):
        device_id_list = self.xiaomusic.get_group_device_id_list(self.group_name)
        self.log.info(f"group_force_stop_xiaoai {device_id_list}")
        tasks = [self.force_stop_xiaoai(device_id) for device_id in device_id_list]
        results = await asyncio.gather(*tasks)
        self.log.info(f"group_force_stop_xiaoai {device_id_list} {results}")
        return results

    async def stop_after_minute(self, minute: int):
        if self._stop_timer:
            self._stop_timer.cancel()
            self._stop_timer = None
            self.log.info("关机定时器已取消")

        async def _do_stop():
            await asyncio.sleep(minute * 60)
            try:
                await self.stop(arg1="notts")
            except Exception as e:
                self.log.exception(f"Execption {e}")

        self._stop_timer = asyncio.create_task(_do_stop())
        await self.do_tts(f"收到,{minute}分钟后将关机")

    def cancel_next_timer(self):
        self.log.info("cancel_next_timer")
        if self._next_timer:
            self._next_timer.cancel()
            self.log.info(f"下一曲定时器已取消 {self.device_id}")
            self._next_timer = None
        else:
            self.log.info("下一曲定时器不见了")

    def cancel_group_next_timer(self):
        devices = self.xiaomusic.get_group_devices(self.group_name)
        self.log.info(f"cancel_group_next_timer {devices}")
        for device in devices.values():
            device.cancel_next_timer()

    def get_cur_play_list(self):
        return self.device.cur_playlist

    # 清空所有定时器
    def cancel_all_timer(self):
        self.log.info("in cancel_all_timer")
        if self._next_timer:
            self._next_timer.cancel()
            self._next_timer = None
            self.log.info("cancel_all_timer _next_timer.cancel")

        if self._stop_timer:
            self._stop_timer.cancel()
            self._stop_timer = None
            self.log.info("cancel_all_timer _stop_timer.cancel")

    @classmethod
    def dict_clear(cls, d):
        for key in list(d):
            val = d.pop(key)
            val.cancel_all_timer()

    # 根据当前歌曲匹配歌曲列表
    def find_cur_playlist(self, name):
        # 匹配顺序：
        # 1. 收藏
        # 2. 最近新增
        # 3. 排除（全部,所有歌曲,所有电台,临时搜索列表）
        # 4. 所有歌曲
        # 5. 所有电台
        # 6. 全部
        if name in self.xiaomusic.music_list.get("收藏", []):
            return "收藏"
        if name in self.xiaomusic.music_list.get("最近新增", []):
            return "最近新增"
        for list_name, play_list in self.xiaomusic.music_list.items():
            if (list_name not in ["全部", "所有歌曲", "所有电台", "临时搜索列表"]) and (
                name in play_list
            ):
                return list_name
        if name in self.xiaomusic.music_list.get("所有歌曲", []):
            return "所有歌曲"
        if name in self.xiaomusic.music_list.get("所有电台", []):
            return "所有电台"
        return "全部"
