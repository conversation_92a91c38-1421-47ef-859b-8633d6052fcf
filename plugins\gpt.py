import time
import asyncio
import threading
import json
from openai import OpenAI, InternalServerError
from . import Plugin

class GPT(Plugin):
    BASE_URL = 'http://localhost:8000/v1/'
    PROMPT = "你是一个人工智能音箱，根据用户的提问回答问题，每次回答字数不超过100，必要时减少字数方便输出语音。现在先来一个简短的自我介绍。"

    def __init__(self) -> None:
        super().__init__()
        self.last_call_time_ = -1
        self.ai_client_ = OpenAI(base_url=GPT.BASE_URL, api_key='')
        self.ai_model_ = None

    @property
    def is_running(self) -> bool:
        self.last_call_time_ > 0

    @is_running.setter
    def is_running(self, v):
        self.last_call_time_ = time.time() if v else -1

    async def check_full_match_cmd(self, did: str, msg: str, ctrl_panel: bool = False) -> bool:
        if not self.is_running:
            if msg == "打开AI":
                who = await self.ask_ai(GPT.PROMPT)
                await self.start_ai()
                self.do_tts(who)
                return True
        elif msg == "退出" or msg == "关闭":
            self.stop_ai()

    async def check_pattern_match_cmd(self, did: str, msg: str, ctrl_panel: bool = False) -> bool:
        if not self.is_running:
            return False

    async def ask_ai(self, msg: str, kill= False) -> str:
        response = self.ai_client_.chat.completions.create(
            messages = [{"role": "user", "content": msg}],
            stream   = False,
            agent_id = "naQivTmsDa",
            chat_id  = self.ai_model_,
            should_remove_conversation = kill,
        )
        answer = ''
        for v in response.split('\r\n\r\n'):
            if v == 'data: [DONE]':
                break
            if not v.startswith('data:'):
                continue
            data = json.loads(v.encode('utf-8')[6:])
            if not self.ai_model_:
                self.ai_model_ = data.get('model', None)
            choices = data.get('choices', {})
            for item in choices:
                delta = item.get('delta', {})
                content = delta.get('content', '')
                if content:
                    content = json.loads(content)
                    answer += content.get('msg', '')
        return answer

    async def start_ai(self):
        if not self.is_running:
            self.is_running = True
            self.watch()


    async def stop_ai(self):
        if not self.is_running:
            return
        self.is_running = False
        self.do_tts("已退出AI模式")
        await asyncio.sleep(1.1)

        try:
            self.ask_ai("再见", True)
        except Exception:
            pass


    async def watch(self):
        async def timeout():
            while self.is_running:
                await asyncio.sleep(1)
                t = time.time()
                if t - self.last_call_time_ > 30:
                    self.stop_ai()
        def run_async_task():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(timeout())
        # 在后台线程中启动任务
        threading.Thread(target=run_async_task, daemon=False).start()
