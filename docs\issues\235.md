---
title: xiaomusic立体声
---

# xiaomusic立体声

有多个不同版本的小爱，怎么能选择多个音箱一起播放？

## 评论


### 评论 1 - hanxi

参考这个文档，配到一个组里就能同时播放，但是会有播放进度不一致的情况。 /issues/65.html#issuecomment-2215736529

---

### 评论 2 - F-loat

我这边先用一个音箱播放，然后米家里设置全屋播放，就能多个音箱同时播了，进度也同步，而且后续会自动全屋播放

---

### 评论 3 - zazhi4

我遇到双音箱播放问题，没法立体声，没法全屋播放，详述如下：
1、基本情况：原有小爱音箱Pro，LX06，用xiaomusic，0.3.69版本，播放正常。看到issue里提及了立体声，打算尝试，新购了小爱音箱,L06A。将新音箱通过米家APP加入到自家的wifi里。
2、尝试：
2.1，在xiaomusic里，默认面板，设置，发现可以在小爱音箱设置面板里有两个音箱供选择，我都勾选了，在设备分组配置里，两个音箱的did输入，配为1个组，能实现同时驱动两个音箱发声，但是不同步，时间上差了不到1秒钟，但是听起来不舒服。
2.2 在xiaomusic里，默认面板，设置，只勾选1个音箱；设备分组配置里，删除原先配置，清空。播放，只有一只音箱有声音。然后回到米家APP。将两个音箱设置，组成立体声。完成后，依然只有一只音箱有声音。设置后，对播放没有影响。
2.3 在米家APP中，取消立体声配对，改为“全屋播放”。设置好后，依然只有一只音箱有声音。换言之，设置后对播放没有影响。
3、检查版本，在小米音箱app中，检查两只音箱的版本，均为最新稳定版。
![1736217346124](https://gproxy.hanxi.cc/proxy/user-attachments/assets/1fa61ded-0044-4577-8b3f-76d03bfe2453)
![1736217346117](https://gproxy.hanxi.cc/proxy/user-attachments/assets/ad3d13ea-0070-4fee-a2d8-814242efd9f8)
![1736217346120](https://gproxy.hanxi.cc/proxy/user-attachments/assets/6b43db2f-80fd-4aa7-97c7-3edaee304a5f)
![1736217346108](https://gproxy.hanxi.cc/proxy/user-attachments/assets/a274b712-d15b-4e6f-b036-c16e291b841f)
![1736217346113](https://gproxy.hanxi.cc/proxy/user-attachments/assets/194deb50-c0f6-4c09-a241-ab2360301c51)
![1736217346103](https://gproxy.hanxi.cc/proxy/user-attachments/assets/675e53fb-2f33-4c9f-9db2-39a969d549bf)




---

### 评论 4 - zazhi4

再补充一点信息，我做的尝试，以及网上都到的信息。
1、配立体声，配好以后，我用了当前页面的“立体声音效测试”，两个音箱都能发声，声音有先后，有不同，有联系，形成了立体声效果。
2、配立体声，配好以后，还在米家app，用qq音乐放了一首歌，两个音箱都能发声，有立体声效果。
3、网上查询，一些信息均表明，只有qq或其他在米家里播放的，换言之，网络来的信息，通过米家，向音箱播音，能实现立体声。其他方法，比如蓝牙，或AUX，都没法实现立体声。推测，是米家主动发音的时候，内部拆解了左右声道的声音，发往左右音箱。
4、原本猜想，两个音箱，会一主一从，主音箱拆分声音，发往从音箱。但是在米家APP里配置立体声时，未发现主从之分。

---

### 评论 5 - hanxi

全屋播放，控制两个音箱中的任意一个都不行吗？

---

### 评论 6 - zazhi4

全屋播放，设置音箱A（设置界面只选A，当然只能选一个。共有3个选项，音箱A，音箱B，手机）播放，音箱B没声音；设置音箱B播放，音箱A没声音。

---

### 评论 7 - Bazinga-git

组立体声是刚需，大佬加油

---

### 评论 8 - zazhi4

立体声，想了几个路径：1，xiaomusic能不能拆分声音为左右两个声道，分别发往两个音箱，难题在于，怎么保证两个音箱同时接到信息，发声；2，能不能搞定米家app的接口，发送声音给米家app，由米家app发给音箱；3，或是搞定模拟米家app与音箱的接口，通过米家接口，发给音箱，让音箱以为声音来自米家app。方法2，3，要搞清楚，米家app播音，为什么能实现立体声，然后在方法2,3中选一个方向。

---

### 评论 9 - hanxi

@zazhi4 思路是对的，感兴趣可以抓包玩玩的。目前协议是没加密的。

---

### 评论 10 - winsel

我有两个l06a，在米家app里打开dlan并且两个音箱组立体声后，用安卓手机上面的椒盐音乐播放器，推送到小爱音箱dlan的时候显示的是立体声名字，推送后立体声播放，左右延迟很小，几乎察觉不到，相对群组功能好太多了。xiaomusic能不能增加一个立体声选项，采用dlan推送模式播放呢？
这样应该是立体声最优解了
![Image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/3f77e6a3-f65f-407a-84d4-481182994992)


---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/235)
