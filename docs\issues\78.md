---
title: 已支持配置自定义网络歌单，在这里分享你的歌单
---

# 已支持配置自定义网络歌单，在这里分享你的歌单

设置页面新增一个输入框配置json格式，可以定义配置音乐源，可以是电台或者其他的m3u8格式的。
再加一个输入框配置这个json文件的url，点击获取按钮把url对应的json内容填充到json输入框，方便直接使用别人分享的歌单。

比如这样的链接
- https://lhttp.qtfm.cn/live/4915/64k.mp3
- http://ngcdn001.cnr.cn/live/zgzs/index.m3u8

已经测试能播放出来:
```
python3 micli.py play http://ngcdn001.cnr.cn/live/zgzs/index.m3u8
```

预计歌单格式是这样的, type 为 radio 作为电台的设定，会一直播放当前电台，不会播放下一首。
```json
[
  {
    "name":"歌单1",
    "musics":[
      {
        "name":"歌名1",
        "url":"http://ngcdn001.cnr.cn/live/zgzs/index.m3u8",
        "type":"radio"
      },
      {
        "name":"歌名2",
        "url":"https://lhttp.qtfm.cn/live/4915/64k.mp3"
      }
    ]
  },
  {
    "name":"歌单2",
    "musics":[
      {
        "name":"歌名3",
        "url":"https://lhttp.qtfm.cn/live/4915/64k.mp3"
      },
      {
        "name":"歌名4",
        "url":"https://lhttp.qtfm.cn/live/4915/64k.mp3"
      }
    ]
  }
]
```

这里分享一个让 chatgpt 写 python 脚本来生成歌单的例子 <https://chatgpt.com/share/6751c019-74c0-800a-a978-a20c636d4464> 。

## 评论


### 评论 1 - hanxi

可以使用 gist 来配置和分享 json 文件，比如 https://gist.github.com/hanxi/dda82d964a28f8110f8fba81c3ff8314

点击 raw 得到 json 文件的链接 https://gist.githubusercontent.com/hanxi/dda82d964a28f8110f8fba81c3ff8314/raw/8787844d81c39dbfaad4e37954dd449d8bba5728/example.json

当然还可以用其他工具分享json文件，比如 github 和国内的 gitee 。

---

### 评论 2 - hanxi

已经有工具支持将 m3u 格式的电台文件转为网络歌单格式，见 /issues/88.html

欢迎有兴趣的朋友制作其他格式转换工具，比如网易歌单那一类的。

---

### 评论 3 - lazybabyz

按照教程设置后 播放列表选择m3u电台 再选择播放列表 歌曲，结果显示播放中 不断在转台
stdout: [08:58:02] [0.3.37] [INFO] 10.0.80.191:24020 - "GET /playingmusic?did=********* HTTP/1.1" 200
stdout: [08:58:04] [0.3.37] [INFO] 10.0.80.191:24020 - "GET /playingmusic?did=********* HTTP/1.1" 200
stdout: [08:58:04] [0.3.37] [INFO] 10.0.80.191:24020 - "GET /static/default/index.html HTTP/1.1" 304
stdout: [08:58:04] [0.3.37] [INFO] 10.0.80.191:24020 - "GET /getsetting HTTP/1.1" 200
stdout: [08:58:04] [0.3.37] [INFO] 10.0.80.191:24022 - "GET /getversion HTTP/1.1" 200
stdout: [08:58:04] [0.3.37] [INFO] 10.0.80.191:24020 - "GET /musiclist HTTP/1.1" 200
stdout: [08:58:04] [0.3.37] [INFO] 10.0.80.191:24021 - "GET /playingmusic?did=********* HTTP/1.1" 200
stdout: [08:58:04] [0.3.37] [INFO] 10.0.80.191:24020 - "GET /static/default/curplaylist?did=********* HTTP/1.1" 404
stdout: [08:58:05] [0.3.37] [INFO] 10.0.80.191:24022 - "GET /getvolume?did=********* HTTP/1.1" 500
stderr: [08:58:05] [0.3.37] [ERROR] Exception in ASGI application
stderr: Traceback (most recent call last):
stderr:   File "/app/.venv/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py", line 406, in run_asgi
stderr:     result = await app(  # type: ignore[func-returns-value]
stderr:   File "/app/.venv/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py", line 70, in __call__
stderr:     return await self.app(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/fastapi/applications.py", line 1054, in __call__
stderr:     await super().__call__(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/applications.py", line 113, in __call__
stderr:     await self.middleware_stack(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/middleware/errors.py", line 187, in __call__
stderr:     raise exc
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/middleware/errors.py", line 165, in __call__
stderr:     await self.app(scope, receive, _send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/middleware/cors.py", line 85, in __call__
stderr:     await self.app(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
stderr:     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/_exception_handler.py", line 62, in wrapped_app
stderr:     raise exc
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/_exception_handler.py", line 51, in wrapped_app
stderr:     await app(scope, receive, sender)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/routing.py", line 715, in __call__
stderr:     await self.middleware_stack(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/routing.py", line 735, in app
stderr:     await route.handle(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/routing.py", line 288, in handle
stderr:     await self.app(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/routing.py", line 76, in app
stderr:     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/_exception_handler.py", line 62, in wrapped_app
stderr:     raise exc
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/_exception_handler.py", line 51, in wrapped_app
stderr:     await app(scope, receive, sender)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/routing.py", line 73, in app
stderr:     response = await f(request)
stderr:   File "/app/.venv/lib/python3.10/site-packages/fastapi/routing.py", line 301, in app
stderr:     raw_response = await run_endpoint_function(
stderr:   File "/app/.venv/lib/python3.10/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
stderr:     return await dependant.call(**values)
stderr:   File "/app/xiaomusic/httpserver.py", line 124, in getvolume
stderr:     volume = await xiaomusic.get_volume(did=did)
stderr:   File "/app/xiaomusic/xiaomusic.py", line 809, in get_volume
stderr:     return await self.devices[did].get_volume()
stderr:   File "/app/xiaomusic/xiaomusic.py", line 1400, in get_volume
stderr:     playing_info = await self.xiaomusic.mina_service.player_get_status(
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 103, in player_get_status
stderr:     return await self.ubus_request(
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 59, in ubus_request
stderr:     result = await self.mina_request(
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 49, in mina_request
stderr:     return await self.account.mi_request(
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/miaccount.py", line 150, in mi_request
stderr:     raise Exception(f"Error {url}: {resp}")
stderr: Exception: Error https://api2.mina.mi.com/remote/ubus: {'code': 101, 'message': 'ubus server or device returned invalid result', 'data': {'device_data': '{"msg":"Device is offline","code":608}', 'reqID': 'app_ios_YfG6nlPVAtwDHEgMeJXxcOiZQLrR57'}}
stderr: [08:58:05] [0.3.37] [ERROR] h11_impl.py:411: Exception in ASGI application
stderr: Traceback (most recent call last):
stderr:   File "/app/.venv/lib/python3.10/site-packages/uvicorn/protocols/http/h11_impl.py", line 406, in run_asgi
stderr:     result = await app(  # type: ignore[func-returns-value]
stderr:   File "/app/.venv/lib/python3.10/site-packages/uvicorn/middleware/proxy_headers.py", line 70, in __call__
stderr:     return await self.app(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/fastapi/applications.py", line 1054, in __call__
stderr:     await super().__call__(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/applications.py", line 113, in __call__
stderr:     await self.middleware_stack(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/middleware/errors.py", line 187, in __call__
stderr:     raise exc
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/middleware/errors.py", line 165, in __call__
stderr:     await self.app(scope, receive, _send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/middleware/cors.py", line 85, in __call__
stderr:     await self.app(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
stderr:     await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/_exception_handler.py", line 62, in wrapped_app
stderr:     raise exc
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/_exception_handler.py", line 51, in wrapped_app
stderr:     await app(scope, receive, sender)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/routing.py", line 715, in __call__
stderr:     await self.middleware_stack(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/routing.py", line 735, in app
stderr:     await route.handle(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/routing.py", line 288, in handle
stderr:     await self.app(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/routing.py", line 76, in app
stderr:     await wrap_app_handling_exceptions(app, request)(scope, receive, send)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/_exception_handler.py", line 62, in wrapped_app
stderr:     raise exc
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/_exception_handler.py", line 51, in wrapped_app
stderr:     await app(scope, receive, sender)
stderr:   File "/app/.venv/lib/python3.10/site-packages/starlette/routing.py", line 73, in app
stderr:     response = await f(request)
stderr:   File "/app/.venv/lib/python3.10/site-packages/fastapi/routing.py", line 301, in app
stderr:     raw_response = await run_endpoint_function(
stderr:   File "/app/.venv/lib/python3.10/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
stderr:     return await dependant.call(**values)
stderr:   File "/app/xiaomusic/httpserver.py", line 124, in getvolume
stderr:     volume = await xiaomusic.get_volume(did=did)
stderr:   File "/app/xiaomusic/xiaomusic.py", line 809, in get_volume
stderr:     return await self.devices[did].get_volume()
stderr:   File "/app/xiaomusic/xiaomusic.py", line 1400, in get_volume
stderr:     playing_info = await self.xiaomusic.mina_service.player_get_status(
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 103, in player_get_status
stderr:     return await self.ubus_request(
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 59, in ubus_request
stderr:     result = await self.mina_request(
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 49, in mina_request
stderr:     return await self.account.mi_request(
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/miaccount.py", line 150, in mi_request
stderr:     raise Exception(f"Error {url}: {resp}")
stderr: Exception: Error https://api2.mina.mi.com/remote/ubus: {'code': 101, 'message': 'ubus server or device returned invalid result', 'data': {'device_data': '{"msg":"Device is offline","code":608}', 'reqID': 'app_ios_YfG6nlPVAtwDHEgMeJXxcOiZQLrR57'}}
stdout: [08:58:05] [0.3.37] [INFO] 10.0.80.191:24020 - "GET /playingmusic?did=********* HTTP/1.1" 200
stdout: [08:58:05] [0.3.37] [INFO] 10.0.80.191:24021 - "GET /playingmusic?did=********* HTTP/1.1" 200
stdout: [08:58:07] [0.3.37] [INFO] 10.0.80.191:24021 - "GET /playingmusic?did=********* HTTP/1.1" 200
stdout: [08:58:08] [0.3.37] [INFO] 10.0.80.191:24021 - "GET /playingmusic?did=********* HTTP/1.1" 200
stdout: [08:58:08] [0.3.37] [INFO] 10.0.80.191:24020 - "GET /playingmusic?did=********* HTTP/1.1" 200
stderr: [08:58:09] [0.3.37] [INFO] httpserver.py:177: docmd. did:********* cmd:播放列表m3u电台|80后音悦台
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:582: cancel_all_tasks no task
stdout: [08:58:09] [0.3.37] [INFO] 10.0.80.191:24020 - "POST /cmd HTTP/1.1" 200
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:560: 收到消息:播放列表m3u电台|80后音悦台 控制面板:True did:*********
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:648: 匹配到指令. opkey:播放列表 opvalue:play_music_list oparg:m3u电台|80后音悦台
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:716: 根据【m3u电台】找到播放列表【m3u电台】
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:991: 没打乱 m3u电台 ['80后音悦台', 'BBC Radio 1', 'BBC Radio 2', 'BBC Radio 3', 'BBC Radio 4', 'CNA938', 'FM 988', ' AFO Radio', 'BBC News', 'BBC Radio 1 Dance', 'BBC Radio 1 Relax', 'BBC Radio 1Xtra', 'BBC Radio 4 Extra', 'BBC Radio 5 Live', 'BBC Radio 6 Music', 'BCC中广新闻', 'BCC中广流行', 'BCC中广音乐', 'CNA亚洲新闻', 'CNR中国之声', 'CNR乡村之声', 'CNR交通广播', 'CNR台海之声', 'CNR文艺之声', 'CNR湾区之声', 'CNR神州之声', 'CNR经典音乐', 'CNR经济之声', 'CNR老年之声', 'CNR阅读之声', 'CNR音乐之声', 'CRI世界华声', 'CRI华语环球', 'CRI南海之声', 'CRI环球资讯', 'CRI英语资讯', 'CRI轻松调频', 'Capital FM', 'CityFM台之音', 'Cool Radio', 'Gold FM', 'Hit FM劲曲', 'Kiss FM', 'LBC News', 'LBC UK', 'Love FM', 'Love Radio', 'Money FM', 'NPR News', 'News Radio UK', 'One FM', 'Power FM', 'RFA', 'RFI', 'RTI中央广播', 'Times Radio', 'VOA环球英语', 'Yes FM', '上海故事广播', '上海流行音乐', '上海音乐广播', '上海音乐电台', '中文流行', '全国广播', '北京交通广播', '北京体育广播', '北京城市广播', '北京文艺广播', '北京新闻广播', '北京经典调频', '北京音乐广播', '南京音乐广播', '台中广播电台', '四川文艺广播', '国乐悠扬', '天籁之音 Hi-Fi', '天籁古典', '天籁国风', '安全百科', '山西文艺广播', '广东音乐之声', '广州汽车音乐', '摇滚天空', '有声文摘', '民谣蓝调', '民谣音乐台', '江苏故事广播', '江苏文艺广播', '江苏音乐广播', '河北文艺广播', '河北汽车音乐', '深圳音乐广播', '湖南文艺广播', '湖南旅游广播', '湖南音乐之声', '潮流音悦台', '经典FM', '美国之音', '陕西故事广播', '陕西音乐广播']
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:1422: 开始播放列表m3u电台
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:1005: play. search_key: name:80后音悦台
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:663: 根据【80后音悦台】找到歌曲【80后音悦台】
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:1088: cur_music 80后音悦台
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:388: get_music_url web music. name:80后音悦台, url:http://stream3.hndt.com/now/SFZeH2cb/playlist.m3u8
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:360: get_music_sec_url. name:80后音悦台 url:http://stream3.hndt.com/now/SFZeH2cb/playlist.m3u8
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:362: 电台不会有播放时长
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:1437: group_force_stop_xiaoai ['be1daeaa-df03-4a27-8aff-404356abfa9a']
stderr: [08:58:09] [0.3.37] [ERROR] xiaomusic.py:1137: Execption Error https://api2.mina.mi.com/remote/ubus: {'code': 101, 'message': 'ubus server or device returned invalid result', 'data': {'device_data': '{"msg":"Device is offline","code":608}', 'reqID': 'app_ios_10DxZOintrGWhyETo4q9auU3VMNH6A'}}
stderr: Traceback (most recent call last):
stderr:   File "/app/xiaomusic/xiaomusic.py", line 1131, in force_stop_xiaoai
stderr:     ret = await self.xiaomusic.mina_service.player_pause(device_id)
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 79, in player_pause
stderr:     return await self.ubus_request(
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 59, in ubus_request
stderr:     result = await self.mina_request(
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 49, in mina_request
stderr:     return await self.account.mi_request(
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/miaccount.py", line 150, in mi_request
stderr:     raise Exception(f"Error {url}: {resp}")
stderr: Exception: Error https://api2.mina.mi.com/remote/ubus: {'code': 101, 'message': 'ubus server or device returned invalid result', 'data': {'device_data': '{"msg":"Device is offline","code":608}', 'reqID': 'app_ios_10DxZOintrGWhyETo4q9auU3VMNH6A'}}
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:1440: group_force_stop_xiaoai ['be1daeaa-df03-4a27-8aff-404356abfa9a'] [None]
stderr: [08:58:09] [0.3.37] [INFO] xiaomusic.py:1091: 播放 http://stream3.hndt.com/now/SFZeH2cb/playlist.m3u8
stderr: [08:58:10] [0.3.37] [ERROR] xiaomusic.py:1332: Execption Error https://api2.mina.mi.com/remote/ubus: {'code': 101, 'message': 'ubus server or device returned invalid result', 'data': {'device_data': '{"msg":"Device is offline","code":608}', 'reqID': 'app_ios_Bbkf4HLQiN7tjl1rGD2gpXuJ8yI0nP'}}
stderr: Traceback (most recent call last):
stderr:   File "/app/xiaomusic/xiaomusic.py", line 1327, in play_one_url
stderr:     ret = await self.xiaomusic.mina_service.play_by_url(device_id, url)
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 125, in play_by_url
stderr:     return await self.ubus_request(
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 59, in ubus_request
stderr:     result = await self.mina_request(
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/minaservice.py", line 49, in mina_request
stderr:     return await self.account.mi_request(
stderr:   File "/app/.venv/lib/python3.10/site-packages/miservice/miaccount.py", line 150, in mi_request
stderr:     raise Exception(f"Error {url}: {resp}")
stderr: Exception: Error https://api2.mina.mi.com/remote/ubus: {'code': 101, 'message': 'ubus server or device returned invalid result', 'data': {'device_data': '{"msg":"Device is offline","code":608}', 'reqID': 'app_ios_Bbkf4HLQiN7tjl1rGD2gpXuJ8yI0nP'}}
stderr: [08:58:10] [0.3.37] [INFO] xiaomusic.py:1305: group_player_play http://stream3.hndt.com/now/SFZeH2cb/playlist.m3u8 ['be1daeaa-df03-4a27-8aff-404356abfa9a'] [None]
stderr: [08:58:10] [0.3.37] [INFO] xiaomusic.py:1094: 播放 80后音悦台 失败

---

### 评论 4 - hanxi

设备掉线了

---

### 评论 5 - *********

怎么获取 他正在播放什么？或者是播放进度 ？播放列表？我想给他加进去
![233333](https://gproxy.hanxi.cc/proxy/user-attachments/assets/013cd952-69e9-4754-870f-2d5321865179)


---

### 评论 6 - hanxi

> 怎么获取 他正在播放什么？或者是播放进度 ？播放列表？我想给他加进去 ![233333](https://private-user-images.githubusercontent.com/*********/*********-013cd952-69e9-4754-870f-2d5321865179.jpg?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3Mjc4ODY3MzUsIm5iZiI6MTcyNzg4NjQzNSwicGF0aCI6Ii8xMDAxNDI1MTkvMzcyOTI2Mjk2LTAxM2NkOTUyLTY5ZTktNDc1NC04NzBmLTJkNTMyMTg2NTE3OS5qcGc_WC1BbXotQWxnb3JpdGhtPUFXUzQtSE1BQy1TSEEyNTYmWC1BbXotQ3JlZGVudGlhbD1BS0lBVkNPRFlMU0E1M1BRSzRaQSUyRjIwMjQxMDAyJTJGdXMtZWFzdC0xJTJGczMlMkZhd3M0X3JlcXVlc3QmWC1BbXotRGF0ZT0yMDI0MTAwMlQxNjI3MTVaJlgtQW16LUV4cGlyZXM9MzAwJlgtQW16LVNpZ25hdHVyZT0wNzI5OTdhOTAxMmIwMDkxZTBjOGNhYTZkOWVjY2MwZTRmNGE0YTYzNDFhNGY1YzNjNTI4ZWY0YzYzYzc0Nzk3JlgtQW16LVNpZ25lZEhlYWRlcnM9aG9zdCJ9.4-5nGDdkDv9FRp9bAnwN4dzmf4wqKHnG4bW44BhVyRQ)

这个接口 `/playingmusic`

---

### 评论 7 - 114514thD

加不加"type":"radio"都会一直播放不切换到下一首歌是为什么呢

---

### 评论 8 - hanxi

> 加不加"type":"radio"都会一直播放不切换到下一首歌是为什么呢

发出来看看？

---

### 评论 9 - 114514thD

> > 加不加"type":"radio"都会一直播放不切换到下一首歌是为什么呢
> 
> 发出来看看？

~~本地开服务器，生成的m3u列表
格式如下
`#EXTINF:247,周传雄 - 临别一眼.mp3
http://*************:8000/%E5%91%A8%E4%BC%A0%E9%9B%84%20-%20%E4%B8%B4%E5%88%AB%E4%B8%80%E7%9C%BC.mp3`
包含了时长信息
版本是0.3.46
potplayer里播放完全正常~~

仔细研究了一下，发现确实存在问题，不过是另一种情况，下面单说

---

### 评论 10 - 114514thD

> > 加不加"type":"radio"都会一直播放不切换到下一首歌是为什么呢
> 
> 发出来看看？

经过实验发现，本地生成的m3u用potplayer播放正常
![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/754e4344-9262-4ad1-bf17-dd83f5e3b6e5)
转换为json（去掉"type":"radio"）后用小爱播放也正常
![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/9f1a9f02-6cf1-4536-91bd-e5e3677d6513)

但是alist链接就不正常，alist生成的m3u格式如下
`#EXTM3U
#EXTINF:-1,Let Me Hear.mp3
http://192.168.1.198:5244/d/%E7%BD%91%E6%98%93%E4%BA%91%E9%9F%B3%E4%B9%90%20%E9%9F%B3%E4%B9%90%E4%BA%91%E7%9B%98/Let%20Me%20Hear.mp3?sign=xxxx=:0`
没有时长信息，但是用potplayer一播放就出现时长了
![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/34ac4b9f-8b7f-40d7-9ac9-aa4621b59aa3)
而用小爱播放就始终没有时长（切歌、等待都试过了）
![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/a42b62e4-7a48-46b8-b83b-a4c8cb219c0b)
大佬你的示例链接（gist.github.com/hanxi/dda82d964a28f8110f8fba81c3ff8314）里的又是正常的，感觉可能是alist的流比较特殊。。
![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/f84ab805-54a6-40f1-937c-67832ff0b9d6)

---

### 评论 11 - 114514thD

> > 加不加"type":"radio"都会一直播放不切换到下一首歌是为什么呢
> 
> 发出来看看？

这几天再仔细研究了一下，发现一个可能的原因：这样获取到的是m4a文件，我尝试着在json里配置获取到的m4a链接，发现播放同样也是无时长

---

### 评论 12 - hanxi

获取歌曲时长确实有些格式获取不到。

---

### 评论 13 - 114514thD

> 获取歌曲时长确实有些格式获取不到。

http://m7.music.126.net/20241216093525/75c9080afa2929d7eec8e1cdbcbc0a92/yyaac/0709/535a/5358/0c6e2dcac3d0e9fa4415d22e1eca1337.m4a
以这个文件为例，我用ffmpeg可以获取时长等元数据
`
Input #0, mov,mp4,m4a,3gp,3g2,mj2, from 'http://m7.music.126.net/20241216093525/75c9080afa2929d7eec8e1cdbcbc0a92/yyaac/0709/535a/5358/0c6e2dcac3d0e9fa4415d22e1eca1337.m4a':
  Metadata:
    major_brand     : mp42
    minor_version   : 0
    compatible_brands: M4A mp42isom
    creation_time   : 2019-02-21T02:51:36.000000Z
    iTunSMPB        :  00000000 00000920 000003E8 00000000004BE2F8 00000000 00000000 00000000 00000000 00000000 00000000 00000000 00000000
    encoder         : Nero AAC codec / 1.5.4.0
  Duration: 00:03:45.70, start: 0.052971, bitrate: 64 kb/s
  Chapters:
    Chapter #0:0: start 0.105941, end 225.750930
      Metadata:
        title           :
  Stream #0:0[0x1](und): Audio: aac (HE-AAC) (mp4a / 0x6134706D), 44100 Hz, stereo, fltp, 63 kb/s (default)
    Metadata:
      creation_time   : 2019-02-21T02:51:36.000000Z
      handler_name    : Sound Media Handler
      vendor_id       : [0][0][0][0]`
那是为什么播放就不行呢

---

### 评论 14 - hanxi

因为代码有问题。

---

### 评论 15 - 114514thD

> 因为代码有问题。

好吧😂😂，大佬真是直接😁

---

### 评论 16 - hanxi

重构方案 #314 

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/78)
