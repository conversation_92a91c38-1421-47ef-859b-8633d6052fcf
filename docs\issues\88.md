---
title: 如何添加m3u格式文件的电台
---

# 如何添加m3u格式文件的电台

比如可以找到这样的 m3u 电台文件: https://github.com/YueChan/Live/blob/main/Radio.m3u

1. 复制文件内容，粘贴到 m3u 转换工具里，点击转换为 json 格式:

![Screenshot_2024-06-29-11-28-58-904_com android chrome](https://gproxy.hanxi.cc/proxy/hanxi/xiaomusic/assets/1185757/bb812a47-17c5-4483-9234-4cf33367b181)

2. 然后复制 json 内容，粘贴到歌单内容里，点击保存，再返回首页:

![Screenshot_2024-06-29-11-29-22-248_com android chrome](https://gproxy.hanxi.cc/proxy/hanxi/xiaomusic/assets/1185757/2fb4ca44-6b79-4438-9bc6-cfbd01272f20)

3. 在首页点击刷新列表，选择所有电台，再点击播放列表歌曲:

![Screenshot_2024-06-29-11-29-55-621_com android chrome](https://gproxy.hanxi.cc/proxy/hanxi/xiaomusic/assets/1185757/c94e4667-f83e-4cd5-9662-e680316cb5b4)

4. 也可以用口令播放电台: `播放列表所有电台` ，或者口令: `播放歌曲北京城市广播`

## 评论


### 评论 1 - guoxiangke

转换m3u链接： http://127.0.0.1:8090/static/m3u.html 

---

### 评论 2 - guoxiangke

http://127.0.0.1:8090/playurl?did=mydid&url=https://a.com/test.m3u 如果能支持 这么播放m3u 就太完美了 
希望能够支持，谢谢作者。

---

### 评论 3 - hanxi

> http://127.0.0.1:8090/playurl?did=mydid&url=https://a.com/test.m3u 如果能支持 这么播放m3u 就太完美了 希望能够支持，谢谢作者。

不想破坏现有接口，可以考虑用插件的方式来实现。

---

### 评论 4 - lazybabyz

potplayer 测试 https://raw.githubusercontent.com/YueChan/Live/refs/heads/main/Radio.m3u 部分可以听
xiaomusic测试 https://github.com/YueChan/Live/blob/main/Radio.m3u 复制raw文件转换 全部失败不停转台

以下potplayer测试 可以听，xiaomusic测试 复制raw文件转换 全部失败不停转台

https://raw.githubusercontent.com/kaige-cai/live/refs/heads/main/radio.m3u
https://raw.githubusercontent.com/imDazui/Tvlist-awesome-m3u-m3u8/master/m3u/%E5%B9%BF%E6%92%AD%E7%94%B5%E5%8F%B02021.m3u 



---

### 评论 5 - hanxi

检查一下是不是 ipv6 的地址？小米音箱不支持 ipv6

---

### 评论 6 - lazybabyz

> 检查一下是不是 ipv6 的地址？小米音箱不支持 ipv6

是我硬件设置的问题 重新安装了 解决!

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/88)
