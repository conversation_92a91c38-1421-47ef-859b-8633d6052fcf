---
title: 群晖docker安装 xiaomusic
---

# 群晖docker安装 xiaomusic

由于现在群晖已经无法正常下载 docker 里的镜像了，绕了好多弯；现在用 ssh 服务命令拉取镜像来创建容器；

## 1. ssh 输入账号密码进入群晖
## 2. 输入 sudo -i 再次输入密码进入 root 权限
## 3. 输入 docker search xiaomusic来查找到该镜像名；
## 4. 然后输入 docker pull xiaomusic 试试是否能安装，如果不能；就得在命令前加个代理地址；下面列了一些代理地址可以一个个的试

```
docker.fxxk.dedyn.iodocker.io
registry-docker-hub-latest-9vgc.onrender.com
docker.chenby.cn
dockerproxy.com
hub.uuuadc.top
docker.jsdelivr.fyi
docker.registry.cyou
dockerhub.anzu.vip
```

我是用的最下面这个成功的

```
docker pull dockerhub.anzu.vip/hanxi/xiaomusic:latest
```

## 5. 安装完成后就进入群晖 DOCKER 配置 xiaomusic

<img width="491" alt="image" src="https://gproxy.hanxi.cc/proxy/hanxi/xiaomusic/assets/38914725/e318062b-bd70-464c-a8df-8ce3635f2d84">

- MI_HARDWARE=型号 前面第4 步骤获取的
- XIAOMUSIC_SEARCH=搜索方式，我填写的bilisearch: 意思是通过 bilibili 搜索
- MI_DID=前面第4 步骤获取的
- MI_USER=小米账号
- MI_PASS=小米密码
- XIAOMUSIC_FUZZY_MATCH_CUTOFF=模糊匹配，最小为 0.1 最大为 1，越小越模糊，越大越精准

## 6. 配置端口

<img width="757" alt="image (1)" src="https://gproxy.hanxi.cc/proxy/hanxi/xiaomusic/assets/38914725/2b6b9283-296f-4845-a3ff-0ebb11f548b4">

## 7. 映射路径

<img width="737" alt="image (2)" src="https://gproxy.hanxi.cc/proxy/hanxi/xiaomusic/assets/38914725/593718dd-8302-4a69-bec9-36e70f3f0407">



## 评论


### 评论 1 - kiwi5656

MI_DID=前面第4 步骤获取的，第4步骤在哪？

---

### 评论 2 - hanxi

> MI_DID=前面第4 步骤获取的，第4步骤在哪？

不用设置 MI_DID 

---

### 评论 3 - hanxi

国内 docker 镜像

```
docker pull m.daocloud.io/docker.io/hanxi/xiaomusic:latest
docker tag m.daocloud.io/docker.io/hanxi/xiaomusic:latest hanxi/xiaomusic:latest
```

---

### 评论 4 - SenyFish

如果手头上有能科学上网的机器，直接把群辉的代理服务器IP填写成可以科学上网的机器IP+端口，翻墙软件打开允许局域网连接就可以

---

### 评论 5 - SmartElec

文档中 `docker pull dockerhub.anzu.vip/xiaomusic:latest`这句命令写错了
修改为
```
docker pull dockerhub.anzu.vip/hanxi/xiaomusic:latest
```

---

### 评论 6 - hanfz123

![Image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/045ae64b-d8d8-4f1d-9ed1-412af011df0c)
你好。为什么我这个地方的文字不是中文的?求解

---

### 评论 7 - hanxi

@hanfz123 你是不是屏蔽了谷歌的某些域名？

---

### 评论 8 - hanfz123

> [@hanfz123](https://github.com/hanfz123) 你是不是屏蔽了谷歌的某些域名？

不确定，但是我访问谷歌啥的都正常啊

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/101)
