---
title: Jellyfin歌单同步到映射目录
---

# Jellyfin歌单同步到映射目录

因为我一直是用jellyfin 听歌, 下载的都是在Pt网站 按照歌手几十张cd一起下载,所以有非常多不想听的歌,于是歌单功能非常重要,既然Jellyfin本上就创建了歌单,我就写一个Python 来同步这个歌单,大家放在 C:\ProgramData\Jellyfin\Server\data\playlists  或者自定义的playlists文件夹中.


```
import os
import xml.etree.ElementTree as ET
import shutil

#这个文件要放在Jellyfin的C:\ProgramData\Jellyfin\Server\data\playlists  中
#这里要改成想要硬链接到的目录地址
folder_path = r'E:\music2\歌曲列表'

def create_hard_links_from_xml(file_path):
     # 解析 XML 文件
    tree = ET.parse(file_path)
    root = tree.getroot()

    # 获取 LocalTitle 标签的值
    local_title = root.find('LocalTitle').text
    # 构建目标目录路径
    target_dir = os.path.join(folder_path, local_title)

    # 检查目标目录是否存在，如果不存在则创建
    if not os.path.exists(target_dir):
        os.makedirs(target_dir)

    # 遍历所有 PlaylistItem 节点
    for playlist_item in root.findall('.//PlaylistItem'):
        # 获取 Path 标签的值
        file_path = playlist_item.find('Path').text
        if os.path.exists(file_path):
            # 获取文件名
            file_name = os.path.basename(file_path)
            target_file = os.path.join(target_dir, file_name)
            try:
                # 创建硬链接
                os.link(file_path, target_file)
                print(f"成功为 {file_path} 创建硬链接到 {target_file}")
            except FileExistsError:
                print(f"目标文件 {target_file} 已存在，跳过。")
            except Exception as e:
                print(f"为 {file_path} 创建硬链接时出错: {e}")
        else:
            print(f"源文件 {file_path} 不存在，跳过。")

shutil.rmtree(folder_path)

# 检查文件夹是否存在
if not os.path.exists(folder_path):
    # 创建单层或多层文件夹
    os.makedirs(folder_path)
    print(f"文件夹 '{folder_path}' 创建成功")

def print_xml_content():
    # 获取当前目录
    current_dir = os.getcwd()
    # 遍历当前目录及其所有子目录
    for root, dirs, files in os.walk(current_dir):
        for file in files:
            # 检查文件扩展名是否为 .xml
            if file.lower().endswith('.xml'):
                file_path = os.path.join(root, file)
                create_hard_links_from_xml(file_path)

if __name__ == "__main__":
    print_xml_content()
```






## 评论


### 评论 1 - colaKot

创建一个xmllink.py 文件 然后把代码放进去,双击运行就行,当然要装python

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/417)
