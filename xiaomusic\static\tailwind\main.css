/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}
::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}
::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Material Icons 基础样式 */
.material-icons {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 24px !important;
  height: 24px !important;
  font-size: 24px !important;
  line-height: 1 !important;
  overflow: hidden !important;
  vertical-align: middle !important;
}

.material-icons.icon-sm {
  width: 20px !important;
  height: 20px !important;
  font-size: 20px !important;
}

.material-icons.icon-lg {
  width: 28px !important;
  height: 28px !important;
  font-size: 28px !important;
}

/* 按钮样式 */
.btn {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
  line-height: 1 !important;
}

.btn-circle {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  line-height: 1 !important;
  min-height: unset !important;
}

/* 左侧按钮组样式 */
.side-buttons .btn {
  opacity: 0.7;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 2px solid transparent;
  transition: all 0.2s ease;
  background-color: transparent;
  padding: 0.5rem !important;
  min-height: unset !important;
}

.side-buttons .btn .material-icons {
  margin: 0 !important;
  padding: 0 !important;
}

.side-buttons .btn:hover {
  opacity: 1;
  transform: translateX(4px);
  background-color: rgba(59, 130, 246, 0.1);
}

.side-buttons .btn.active {
  opacity: 1;
  background-color: rgb(59, 130, 246);
  color: white;
  border: 2px solid rgb(59, 130, 246);
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.5);
  transform: scale(1.1);
}

.side-buttons .btn.active:hover {
  transform: scale(1.1) translateX(4px);
  background-color: rgb(37, 99, 235);
}

/* 播放控制栏样式 */
.player-controls .btn-circle {
  width: 2.5rem !important;
  height: 2.5rem !important;
}

.player-controls .btn-circle .material-icons {
  margin: 0 !important;
  padding: 0 !important;
}

/* 下拉菜单样式 */
.dropdown-content .material-icons {
  margin-right: 0.5rem !important;
}

/* 其他图标容器样式 */
.flex-center-icon {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
}

[v-cloak] {
  display: none;
}

/* 收藏按钮样式 */
.btn-ghost:not(.text-error) .icon-sm {
  opacity: 0.5;
}
.text-error .icon-sm {
  fill: currentColor;
}

/* 修复手机端播放列表 歌曲名称超长溢出问题 */
.song-list .song-item .min-w-0 {
  width: 1px;
}
