---
title: 采用config.json配置方式
---

# 采用config.json配置方式

docker 方式部署默认推荐使用环境变量的方式来配置参数，如果是自己用命令行启动，目前支持的参数配置比较少，但是是支持 `--config` 参数。

使用 pip 安装 xiaomusic 【0.1.83版本才支持 pip 安装】

```shell
pip install xiaomusic
```

依赖的 ffmpeg 需要自己安装。

![image](https://gproxy.hanxi.cc/proxy/hanxi/xiaomusic/assets/1185757/6912e3ec-c42f-42de-b027-a296f5a26ba1)

仓库中有个 config-example.json 文件，可以把这个文件拷贝为 config.json 然后修改 config.json 里的配置，再用下面的命令启动。

```shell
xiaomusic --config ./config.json
```

默认的 config.json 模板如下：

```json
{
    "hardware": "L07A",
    "account": "",
    "password": "",
    "mi_did": "",
    "cookie": "",
    "verbose": false,
    "music_path": "music",
    "conf_path": null,
    "hostname": "***********",
    "port": 8090,
    "proxy": null,
    "search_prefix": "ytsearch:",
    "ffmpeg_location": "./ffmpeg/bin",
    "active_cmd": "play,random_play,playlocal,play_music_list,stop",
    "exclude_dirs": "@eaDir",
    "music_path_depth": 10,
    "disable_httpauth": true,
    "httpauth_username": "admin",
    "httpauth_password": "admin",
    "music_list_url": "",
    "music_list_json": "",
    "disable_download": false,
    "use_music_api": false,
    "log_file": "/tmp/xiaomusic.txt",
    "fuzzy_match_cutoff": 0.6,
    "enable_fuzzy_match": true
}
```

如果采用 docker compose 启动想用 config.json 的配置方式，可以这样配: 

```yaml
services:
  xiaomusic:
    image: hanxi/xiaomusic
    container_name: xiaomusic
    restart: unless-stopped
    ports:
      - 8090:8090
    volumes:
      - ./music:/app/music
      - ./config.json:/app/config.json
    command: ['--config', '/app/config.json']
```
主要就是把 config.json 文件映射进容器和传 `--config` 参数。

## 评论


### 评论 1 - alitime

正需要，有配置文件方便多了

---

### 评论 2 - hanxi

ffmpeg 如果是用 apt install 这类系统工具安装的，默认会在 /usr/bin 目录下，ffmpeg_location 这个参数就需要设置为 /usr/bin .

目前 armv7 cpu 的 docker 镜像里的 ffmpeg 有问题，最好是用 pip 方式安装运行。

---

### 评论 3 - hanxi

> ffmpeg 如果是用 apt install 这类系统工具安装的，默认会在 /usr/bin 目录下，ffmpeg_location 这个参数就需要设置为 /usr/bin .
> 
> 目前 armv7 cpu 的 docker 镜像里的 ffmpeg 有问题，最好是用 pip 方式安装运行。

armv7 问题已经解决。

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/94)
