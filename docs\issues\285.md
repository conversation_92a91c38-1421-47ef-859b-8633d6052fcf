---
title: 相关工具推荐
---

# 相关工具推荐

- [xhongc/music-tag-web 刮削音乐歌词图片](https://github.com/xhongc/music-tag-web)
- [onlyLTY/dockerCopilot 一键更新容器](https://github.com/onlyLTY/dockerCopilot)
- [tiny-nav 非常简单的导航网站，NAS部署](https://github.com/hanxi/tiny-nav)

## 评论


### 评论 1 - F-loat

借楼加个小程序的图 :partying_face: https://github.com/F-loat/xiaoplayer

### 小程序码

<p>
  <img alt="weapp" src="https://assets-1251785959.cos.ap-beijing.myqcloud.com/xiaoplayer/weappcode.jpg" width="24%" />
</p>

### 截图

<p>
  <img src="https://assets-1251785959.cos.ap-beijing.myqcloud.com/xiaoplayer/screenshot/5.png" width="24%" />
  <img src="https://assets-1251785959.cos.ap-beijing.myqcloud.com/xiaoplayer/screenshot/6.png" width="24%" />
  <img src="https://assets-1251785959.cos.ap-beijing.myqcloud.com/xiaoplayer/screenshot/7.png" width="24%" />
  <img src="https://assets-1251785959.cos.ap-beijing.myqcloud.com/xiaoplayer/screenshot/8.png" width="24%" />
</p>

---

### 评论 2 - hanxi

@F-loat 可以在欢迎页加个链接显示小程序码。

---

### 评论 3 - F-loat

@hanxi 可以的，这样还能自动把 ip 用参数带过来，我有空搞一下

---

### 评论 4 - sparkyuanquan

> 借楼加个小程序的图 🥳 https://github.com/F-loat/xiaoplayer
> 
> ### 小程序码
> <img alt="weapp" width="24%" src="https://camo.githubusercontent.com/0213912ac333b24c09baf04401486b231d5ab58771bb2a7ad00e1d14eb39cdc4/68747470733a2f2f6173736574732d313235313738353935392e636f732e61702d6265696a696e672e6d7971636c6f75642e636f6d2f7869616f706c617965722f7765617070636f64652e6a7067">
> 
> ### 截图
> <img alt="" width="24%" src="https://camo.githubusercontent.com/6e5a3b3b33652ee40a30c33c6fe4bbdad43bbda6f0354fd2b8172778c6cc1b28/68747470733a2f2f6173736574732d313235313738353935392e636f732e61702d6265696a696e672e6d7971636c6f75642e636f6d2f7869616f706c617965722f73637265656e73686f742f352e706e67"> <img alt="" width="24%" src="https://camo.githubusercontent.com/664b337b2ca34a2651fa4b04e7ac40939de6fd8bf9ee2740aaffadb83b619b06/68747470733a2f2f6173736574732d313235313738353935392e636f732e61702d6265696a696e672e6d7971636c6f75642e636f6d2f7869616f706c617965722f73637265656e73686f742f362e706e67"> <img alt="" width="24%" src="https://camo.githubusercontent.com/618c2dce4e280bad096a8105ed1879c26c855872223fcebf11bf66b6bbf552cb/68747470733a2f2f6173736574732d313235313738353935392e636f732e61702d6265696a696e672e6d7971636c6f75642e636f6d2f7869616f706c617965722f73637265656e73686f742f372e706e67"> <img alt="" width="24%" src="https://camo.githubusercontent.com/98b193d9fa4f918d33a9b1166e230e839e962ee8fec087f34b57742c70320881/68747470733a2f2f6173736574732d313235313738353935392e636f732e61702d6265696a696e672e6d7971636c6f75642e636f6d2f7869616f706c617965722f73637265656e73686f742f382e706e67">

你好，麻烦问一下，只能内网播放吗？我在路由上映射了8090端口， 在家外小程序可以看到NAS上的歌曲，也能控制音箱进行播放，，但是手机本机直接播放失败，麻烦问一下，是否可以在外面直接小程序播放NAS音乐，这样就不用安装像音流 DS Audio那些NAS的那些音乐播放器了。

---

### 评论 5 - F-loat

@sparkyuanquan 可以本机直接播放的，之前部分场景判断有问题，应该已经修复了，重启下小程序试试

---

### 评论 6 - sparkyuanquan

小程序删了，重开，我用手机的5G网络微信小程序登录后可以看到歌曲，在手机上播放音乐就转圈，过差不多一分钟左右，显示 INNERERRCODE:-1001，ERRMSG:请求超时。我在路由上把xiaomusic的8090端口映射到了外网60000。

---

### 评论 7 - sparkyuanquan

发现问题在哪了，如果直接映射8090到外网8090，就可以播放，但是如果8090映射到60000不行。我继续试试nginx反代https看看

---

### 评论 8 - sparkyuanquan

原来是要内网服务器地址和公网服务器地址都要填写， 内网写8090端口，公网写60000端口才行，单写公网服务器地址不写内网服务器地址就会有上面的问题。

---

### 评论 9 - F-loat

@sparkyuanquan 我有空再看一下，应该只写公网也要可以的

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/285)
