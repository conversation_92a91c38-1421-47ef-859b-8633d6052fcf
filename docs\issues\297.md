---
title: xiaomusic极空间安装教程（2024/12/28更新）
---

# xiaomusic极空间安装教程（2024-12-28更新）

> 本教程同步更新于最新版的xiaomusic

<s>看不懂/嫌麻烦/懒 但有点小钱，找 hanxi 预约微信或者 QQ 远程安装，他便宜，收费50一次，作法不成功不要钱</s>

**ARM架构自己想办法获取镜像 点名Z2PRO**

# 获取镜像

## 科学环境：

1. 在 **搜索框** 中输入 `hanxi/xiaomusic`，在搜索的结果中直接选择第一个，点击**下载**

![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/ef18b479-aef3-4e76-a299-6f220fc9e549)

2. 在新弹出的版本选择窗口中，根据你的情况选择。
![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/20dd0b64-9223-47ae-a3e4-0b0af05796f8)

### 版本说明
- 获取 **最新版** 直接点击 **下载** 即可，建议使用默认的 `latest`
- 获取 **特定版本** [点击此处可查看](https://github.com/hanxi/xiaomusic/releases)  用于回退出现功能不兼容、恶性bug等情况，一般建议反馈开发者，修复很及时，尽量不要回退版本   请输入如 `v0.3.55`
- 获取 **实验版本**（已修复部分bug但未推送）请输入 `main` 


3. 接着弹出如图所示的页面，耐心等待下载完成。
![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/5ea2621a-bba5-4269-b896-0e6ca323beb8)


4. 下载完成后切换到 **本地镜像** 选项卡

剩余步骤与国内环境相同，见 [部署镜像](#部署镜像)

## 国内环境：
1. 打开docker，在左侧的菜单中选择 **镜像** 切换到 **仓库** 选项卡，点击 **自定义拉取** 按钮
![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/bf4ac64e-ce50-4456-bc7b-05530b5abc1b)
2. 在弹出的对话框中输入   ` m.daocloud.io/docker.io/hanxi/xiaomusic `  ，点击 **拉取** 按钮
![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/a8f91db7-eac9-472d-a920-a77c280bbc5e)
3. 下载完成后切换到 **本地镜像** 选项卡

# 部署镜像
1. 找到刚才已经拉取好的镜像，*单击选中*，点击 **添加到容器**
![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/6e0bcf60-d1aa-46ac-9053-5ad957f3d509)
2. 在弹出的 **创建容器** 菜单中，切换到 **文件夹路径** 选项卡中，按图中的提示进行配置。
![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/cca8bc1c-de8f-4d03-81eb-a0f2b06c121e)

**注意：**
* 装载路径中的 **配置文件目录**  和 **音乐目录** 必须进行配置，**其他目录非必要请勿配置**
* 主题目录为方便开发主题调试时的配置选项，普通用户不能理解明确用途请**不要配置主题目录**，否则会报**HTTP Status 500 – Internal Server Error** 错误
* 如有多个音乐目录，请按照下面的格式进行配置

| 文件/文件夹 | 装载路径 | 
| :----------: | :----------: |
| /data/music1 | /app/music/music1 |
| /data/music2 | /app/music/music2 |

3. 切换到 **端口** 选项卡，修改成与你的极空间 *不冲突* 的本地端口号，如 `5678` （示例按照本地端口号5678来进行配置，下同）
> 友情提醒: 尽量不要修改容器端口号，否则要到配置文件目录修改对应的`setting.json`文件中的配置，会增加很多麻烦

![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/182b32ef-d91f-47c5-ba9d-7ef3542ebf40)

5. 切换到 **环境** 选项卡，将`XIAOMUSIC_HOSTNAME` 修改为你的 **极空间的IP地址** 
> 友情提醒：
> 1.  此处不可忽略，否则后续播放音乐会出现问题
> 2. 不要尝试修改XIAOMUSIC_PORT！除非你没有看上一条的友情提醒
> 3. 不要在此处配置`ACCOUNT`和`PASSWORD`，没有过风控仍然无法使用！上古时代的教程不要再看了，容易走火入魔！

![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/b41b7359-f2b9-4ad8-b2a2-0ce2a1601739)

6. 点击 **应用**按钮，此时容器已经配置完成了，切换到左侧的 **容器概况** 菜单，可查看容器详情
![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/5c5b3497-49d8-4f17-9acb-6d1e551caf4f)

# 进入xiaomusic网页端进行配置
1.请关闭代理，打开浏览器，地址栏输入 **极空间IP:本地端口号** 如`***********:5678`，打开网页后点击 **默认主题**

![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/1b286d0f-f10e-46ff-89a0-cdff9e192f9b)

 **注意：** 
* 不要复制此处的地址，必须输入极空间的IP地址。不知道的建议上咸鱼50块换个不锈钢盆
* 不要输入容器的端口号8090，极空间不能使用这个端口号。

2. 点击 **设置** 按钮进入设置页面
![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/f3aca07a-3663-4bb6-bb93-0329b2d4c433)

3. 输入**小米账号**、**小米密码**、**XIAOMUSIC_HOSTNAME(IP或域名):**、**外网访问端口**，滑到页面最下方点击 **保存**
![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/db03af0c-851a-4185-a7fd-b5b02b6d8d2b)
![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/dd42a653-d364-4eec-9f87-efb3c52e57a7)

**注意:**
* 小米账号非手机号，请在手机设置-个人中心中查看小米ID
* 密码不要输错，账号密码错误在上面会弹出提醒，不要假装看不见上面的提醒文字
* XIAOMUSIC_HOSTNAME(IP或域名): 可以输入当前页面的IP地址（在地址栏），**不要在此处输入端口号！！！**，如果域名需要使用https协议，请加上https://

4.如果以上步骤没错，你将在设置中心看见设备列表
![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/04074894-6599-4b35-95ea-0618ed906d15)

5. 回到首页，出现设备列表，切换对应设备即可畅享
![图片](https://gproxy.hanxi.cc/proxy/user-attachments/assets/1ce4791f-7ae7-40b4-8c90-eca6b0799e19)


## 评论


### 评论 1 - xiaohuobanhahaha

[xiaomusic.txt](https://github.com/user-attachments/files/18011572/xiaomusic.txt)

<img width="559" alt="截屏2024-12-05 00 43 24" src="https://gproxy.hanxi.cc/proxy/user-attachments/assets/160aeacc-e1c0-40fa-b219-6b6f5183c43c">
an'zh
无法使用语音播放歌曲，小爱s12a。极空间z4pro。
1. 按照教程，点击播放本地歌曲，提示hostname和设置的端口映射不匹配。映射5678，容器端口8090.
2.网络host模式，能够本地播放，无法使用语音控制，提示“下载app”。日志已上传

---

### 评论 2 - 52fisher

> [xiaomusic.txt](https://github.com/user-attachments/files/18011572/xiaomusic.txt)
> <img alt="截屏2024-12-05 00 43 24" width="559" src="https://private-user-images.githubusercontent.com/20666294/392485798-160aeacc-e1c0-40fa-b219-6b6f5183c43c.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzMzMzE3NzIsIm5iZiI6MTczMzMzMTQ3MiwicGF0aCI6Ii8yMDY2NjI5NC8zOTI0ODU3OTgtMTYwYWVhY2MtZTFjMC00MGZhLWIyMTktNmI2ZjUxODNjNDNjLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDEyMDQlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQxMjA0VDE2NTc1MlomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWJhMmZjMzFhMzdmYmI1NGJjZTg1ZGVhNGI2Njc1YjYwYmQxZjVmMzYyYjg3YWNlNzdhNmEwMzE4Y2UyMTRlNjUmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0._OSLH4Bc0stzG4tLdPalW-mB-ASKlM9uztZ1icefPFU"> an'zh 无法使用语音播放歌曲，小爱s12a。极空间z4pro。 1. 按照教程，点击播放本地歌曲，提示hostname和设置的端口映射不匹配。映射5678，容器端口8090. 2.网络host模式，能够本地播放，无法使用语音控制，提示“下载app”。日志已上传

既然你映射的5678，为什么你又在那把监听端口改成11999?  我的教程里面全程没有写要修改监听端口

---

### 评论 3 - xiaohuobanhahaha

我没讲清楚。我试了两种极空间的桥接和host模式。桥接模式。我按照教程走的。报错如图
<img width="847" alt="截屏2024-12-05 01 46 52" src="https://gproxy.hanxi.cc/proxy/user-attachments/assets/e7f58907-a216-41e8-bafa-5d49db8eca45">
<img width="516" alt="截屏2024-12-05 01 49 11" src="https://gproxy.hanxi.cc/proxy/user-attachments/assets/4261c2e2-fe0c-4ff6-ae06-ead7f928af57">
<img width="647" alt="截屏2024-12-05 01 47 02" src="https://gproxy.hanxi.cc/proxy/user-attachments/assets/35b195d1-9512-40bb-b336-847e0bb2e6c9">
<img width="667" alt="截屏2024-12-05 01 47 15" src="https://gproxy.hanxi.cc/proxy/user-attachments/assets/b917a977-38cf-4126-8754-c46abe9360a2">

提到的第二个问题和日志，是我将网络模式改为host的情况，能连上音箱，但是没法使用语音控制。


---

### 评论 4 - 52fisher

> 我没讲清楚。我试了两种极空间的桥接和host模式。桥接模式。我按照教程走的。报错如图 <img alt="截屏2024-12-05 01 46 52" width="847" src="https://private-user-images.githubusercontent.com/20666294/392507064-e7f58907-a216-41e8-bafa-5d49db8eca45.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzMzMzQ4OTEsIm5iZiI6MTczMzMzNDU5MSwicGF0aCI6Ii8yMDY2NjI5NC8zOTI1MDcwNjQtZTdmNTg5MDctYTIxNi00MWU4LWJhZmEtNWQ0OWRiOGVjYTQ1LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDEyMDQlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQxMjA0VDE3NDk1MVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTBlMTYxZGI0ZjRiYTkyOGIwZWQ4YWZlZWJiY2U3MzljZTg5NTNhZjJkNzlkNzYyYzlmMWJkZjlkMGYwNWEzNzgmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.XSsE7pZj7pwj4kaZBQ02fyH13bVXLzGuJcjRv98WWiQ"> <img alt="截屏2024-12-05 01 49 11" width="516" src="https://private-user-images.githubusercontent.com/20666294/392507855-4261c2e2-fe0c-4ff6-ae06-ead7f928af57.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzMzMzQ4OTEsIm5iZiI6MTczMzMzNDU5MSwicGF0aCI6Ii8yMDY2NjI5NC8zOTI1MDc4NTUtNDI2MWMyZTItZmUwYy00ZmY2LWFlMDYtZWFkN2Y5MjhhZjU3LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDEyMDQlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQxMjA0VDE3NDk1MVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTk2NzFjYTRmNzAyMTJlYzYwZDI4NThlMWQ1MDViZGU5MDI3YThjNzExZTAyNWJmM2NlYWQzZDIzYzRhMjc1MTcmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.NIBWfzpZ5OTIic6Gv83_PrOdUX27o19Vo1zDvFyrILE"> <img alt="截屏2024-12-05 01 47 02" width="647" src="https://private-user-images.githubusercontent.com/20666294/392507111-35b195d1-9512-40bb-b336-847e0bb2e6c9.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzMzMzQ4OTEsIm5iZiI6MTczMzMzNDU5MSwicGF0aCI6Ii8yMDY2NjI5NC8zOTI1MDcxMTEtMzViMTk1ZDEtOTUxMi00MGJiLWIzMzYtODQ3ZTBiYjJlNmM5LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDEyMDQlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQxMjA0VDE3NDk1MVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWI5YTliOWQzNDUyYThjODYwZmY4NTJiMTNkYmFmNmY3ZWE5ZDBlMmQ5OGQxMTIzM2JlMmIxZTcwNTNlMmYwZTEmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.f9czCn43Yzm7sn6-cpJykaWIng4WJf9aoE52kbVeASY"> <img alt="截屏2024-12-05 01 47 15" width="667" src="https://private-user-images.githubusercontent.com/20666294/392507187-b917a977-38cf-4126-8754-c46abe9360a2.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzMzMzQ4OTEsIm5iZiI6MTczMzMzNDU5MSwicGF0aCI6Ii8yMDY2NjI5NC8zOTI1MDcxODctYjkxN2E5NzctMzhjZi00MTI2LTg3NTQtYzQ2YWJlOTM2MGEyLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDEyMDQlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQxMjA0VDE3NDk1MVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWQ3MDE1NjVhZjMzMTRkNjg5ZTA5NDc1MDU3OTFiODc3NTYyMTg3Y2FjNjg2NGM3MjE0N2VlNjg0YzFmZTgwZGImWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.tX8iIlpvE43Krq4q__7dQ3Wuz8kYQuzmlf-XXNHO1Ws">
> 
> 提到的第二个问题和日志，是我将网络模式改为host的情况，能连上音箱，但是没法使用语音控制。

把外网访问端口改成5678

---

### 评论 5 - xiaohuobanhahaha

可以连接上网页控制了，但是语音控制仍然不行。已经按照FAQ问题集合 #99 重启docker。
这是日志，
[xiaomusic.txt](https://github.com/user-attachments/files/18012369/xiaomusic.txt)

---

### 评论 6 - 52fisher

> 可以连接上网页控制了，但是语音控制仍然不行。已经按照FAQ问题集合 #99 重启docker。 这是日志， [xiaomusic.txt](https://github.com/user-attachments/files/18012369/xiaomusic.txt)


你的设置 hostname='**************', port=8090, public_port=5678, 
后台的ip  ************** 检查一下你的地址  有可能是你的ip地址改变了

---

### 评论 7 - xiaohuobanhahaha

> > 可以连接上网页控制了，但是语音控制仍然不行。已经按照FAQ问题集合 #99 重启docker。 这是日志， [xiaomusic.txt](https://github.com/user-attachments/files/18012369/xiaomusic.txt)
> 
> 你的设置 hostname='**************', port=8090, public_port=5678, 后台的ip ************** 检查一下你的地址 有可能是你的ip地址改变了

确实是变了。**************是我电脑的ip。 hostname='**************'是极空间的。小爱是*************。现在我的网络结构是电脑连nas上的istoreos旁路由。nas直连主路由，小爱直连主路由。主路由dhcp都绑定了。 大佬，这种情况该怎么解决呢。所有设置都是默认，没修改哈。

---

### 评论 8 - 52fisher

> 确实是变了。**************是我电脑的ip。 hostname='**************'是极空间的。小爱是*************。现在我的网络结构是电脑连nas上的istoreos旁路由。nas直连主路由，小爱直连主路由。主路由dhcp都绑定了。 大佬，这种情况该怎么解决呢。所有设置都是默认，没修改哈。

容器的网络模式改成bridge试试呢  没解决的话你加群明天再详聊吧

---

### 评论 9 - xiaohuobanhahaha

<img width="660" alt="截屏2024-12-05 02 23 53" src="https://gproxy.hanxi.cc/proxy/user-attachments/assets/b9d26de9-3dcf-4e65-9460-36603735c887">
<img width="780" alt="截屏2024-12-05 02 24 49" src="https://gproxy.hanxi.cc/proxy/user-attachments/assets/6a204cdb-bb10-4f35-822d-613aeed0fae0">


> > 确实是变了。**************是我电脑的ip。 hostname='**************'是极空间的。小爱是*************。现在我的网络结构是电脑连nas上的istoreos旁路由。nas直连主路由，小爱直连主路由。主路由dhcp都绑定了。 大佬，这种情况该怎么解决呢。所有设置都是默认，没修改哈。
> 
> 容器的网络模式改成bridge试试呢 没解决的话你加群明天再详聊吧

辛苦了，这么晚还在回复。我一直用的bridge。大佬，群号多少，不行我明天群里问吧。

---

### 评论 10 - 52fisher

> <img alt="截屏2024-12-05 02 23 53" width="660" src="https://private-user-images.githubusercontent.com/20666294/392519509-b9d26de9-3dcf-4e65-9460-36603735c887.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzMzMzcwNzEsIm5iZiI6MTczMzMzNjc3MSwicGF0aCI6Ii8yMDY2NjI5NC8zOTI1MTk1MDktYjlkMjZkZTktM2RjZi00ZTY1LTk0NjAtMzY2MDM3MzVjODg3LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDEyMDQlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQxMjA0VDE4MjYxMVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWFhN2I4NzQyYjA4YzRiOTk4OTU3NmVkNjU2MjM1ODhjMmNlOWU0YTg5OTFlMzA1NTcxMTA5OTk1YjgwNThhOTgmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.yrC-sn2T6PKwBJb_mal1T2yxcSz008Hb7KWVmOe6zaA"> <img alt="截屏2024-12-05 02 24 49" width="780" src="https://private-user-images.githubusercontent.com/20666294/392519852-6a204cdb-bb10-4f35-822d-613aeed0fae0.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzMzMzcwNzEsIm5iZiI6MTczMzMzNjc3MSwicGF0aCI6Ii8yMDY2NjI5NC8zOTI1MTk4NTItNmEyMDRjZGItYmIxMC00ZjM1LTgyMmQtNjEzYWVlZDBmYWUwLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDEyMDQlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQxMjA0VDE4MjYxMVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTE4MzlhODM1NDY4ZjFmN2I2Y2JkZWU5ZGFkNTNjYTNmZDg2OTU3YzA0MjRkMDA2MzRmOTk2ZGE3NmE2NjViZmImWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0._I8Rll2a96i-cx6WBvEwOclNInfOmZkD5HmcorzT-KI">
> 
> > > 确实是变了。**************是我电脑的ip。 hostname='**************'是极空间的。小爱是*************。现在我的网络结构是电脑连nas上的istoreos旁路由。nas直连主路由，小爱直连主路由。主路由dhcp都绑定了。 大佬，这种情况该怎么解决呢。所有设置都是默认，没修改哈。
> > 
> > 
> > 容器的网络模式改成bridge试试呢 没解决的话你加群明天再详聊吧
> 
> 辛苦了，这么晚还在回复。我一直用的bridge。大佬，群号多少，不行我明天群里问吧。

[readme](https://github.com/hanxi/xiaomusic?tab=readme-ov-file#-%E8%AE%A8%E8%AE%BA%E5%8C%BA)

---

### 评论 11 - xiaohuobanhahaha

> > <img alt="截屏2024-12-05 02 23 53" width="660" src="https://private-user-images.githubusercontent.com/20666294/392519509-b9d26de9-3dcf-4e65-9460-36603735c887.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzMzMzcwNzEsIm5iZiI6MTczMzMzNjc3MSwicGF0aCI6Ii8yMDY2NjI5NC8zOTI1MTk1MDktYjlkMjZkZTktM2RjZi00ZTY1LTk0NjAtMzY2MDM3MzVjODg3LnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDEyMDQlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQxMjA0VDE4MjYxMVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWFhN2I4NzQyYjA4YzRiOTk4OTU3NmVkNjU2MjM1ODhjMmNlOWU0YTg5OTFlMzA1NTcxMTA5OTk1YjgwNThhOTgmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.yrC-sn2T6PKwBJb_mal1T2yxcSz008Hb7KWVmOe6zaA"> <img alt="截屏2024-12-05 02 24 49" width="780" src="https://private-user-images.githubusercontent.com/20666294/392519852-6a204cdb-bb10-4f35-822d-613aeed0fae0.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3MzMzMzcwNzEsIm5iZiI6MTczMzMzNjc3MSwicGF0aCI6Ii8yMDY2NjI5NC8zOTI1MTk4NTItNmEyMDRjZGItYmIxMC00ZjM1LTgyMmQtNjEzYWVlZDBmYWUwLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNDEyMDQlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjQxMjA0VDE4MjYxMVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTE4MzlhODM1NDY4ZjFmN2I2Y2JkZWU5ZGFkNTNjYTNmZDg2OTU3YzA0MjRkMDA2MzRmOTk2ZGE3NmE2NjViZmImWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0._I8Rll2a96i-cx6WBvEwOclNInfOmZkD5HmcorzT-KI">
> > > > 确实是变了。**************是我电脑的ip。 hostname='**************'是极空间的。小爱是*************。现在我的网络结构是电脑连nas上的istoreos旁路由。nas直连主路由，小爱直连主路由。主路由dhcp都绑定了。 大佬，这种情况该怎么解决呢。所有设置都是默认，没修改哈。
> > > 
> > > 
> > > 容器的网络模式改成bridge试试呢 没解决的话你加群明天再详聊吧
> > 
> > 
> > 辛苦了，这么晚还在回复。我一直用的bridge。大佬，群号多少，不行我明天群里问吧。
> 
> [readme](https://github.com/hanxi/xiaomusic?tab=readme-ov-file#-%E8%AE%A8%E8%AE%BA%E5%8C%BA)

已自查解决。问题是账号问题。绑定设备的一定是创建者，不能是管理员。

---

### 评论 12 - McCree2020

这个主题目录不能设置吧？没人遇到这个issue？我原来用的张大妈平台的教程设置的，能用，后来看到这个教程后就修改了后台的路径映射，但是dockers启动正常，网页不能打开提示internal sever error，后来ssh进docker看了日志文件 提示static那个路径有问题，下边的index什么的文件找不到， 删除主题映射以后，重启docker后，网页正常显示了

---

### 评论 13 - 52fisher

> 这个主题目录不能设置吧？没人遇到这个issue？我原来用的张大妈平台的教程设置的，能用，后来看到这个教程后就修改了后台的路径映射，但是dockers启动正常，网页不能打开提示internal sever error，后来ssh进docker看了日志文件 提示static那个路径有问题，下边的index什么的文件找不到， 删除主题映射以后，重启docker后，网页正常显示了

要注意看提示：
装载路径中的 配置文件目录 和 音乐目录 必须进行配置。

其他的路径非必要不要配置，主题目录路径是方便开发调试的时候用的，普通用户不要映射主题目录。我已经把这个提示更新到文档中了

---

### 评论 14 - zxhans

就不能让xiaomusic支持服务器部署吗？服务器部署为啥设备不能读取呢？home assistant 通过xiaomi home assistant都可以读取呀

---

### 评论 15 - hanxi

> 就不能让xiaomusic支持服务器部署吗？服务器部署为啥设备不能读取呢？home assistant 通过xiaomi home assistant都可以读取呀

支持服务器部署的，你需要在服务器上装个浏览器登陆过风控。

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/297)
