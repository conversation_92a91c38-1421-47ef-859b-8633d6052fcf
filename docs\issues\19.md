---
title: 如何修改默认的8090端口
---

# 如何修改默认的8090端口

docker-compose 修改映射端口会播放失败

```
ports:
      - 80:8090
```

从日志看继续调用了 http://10.0.0.4:8090 而不是修改映射的80，还原成

```
ports:
      - 8090:8090
```
则一切正常

```
xiaomusic    | [BiliBiliSearch] Playlist 安河桥北: Downloading 1 items of 1
xiaomusic    | [download] Downloading item 1 of 1
xiaomusic    | [BiliBili] Extracting URL: http://www.bilibili.com/video/av319943893
xiaomusic    | [BiliBili] 319943893: Downloading webpage
xiaomusic    | 10.0.0.118 - - [21/Feb/2024 15:29:24] "POST /cmd HTTP/1.1" 200 -
xiaomusic    | 10.0.0.118 - - [21/Feb/2024 15:29:24] "POST /cmd HTTP/1.1" 200 -
xiaomusic    | 10.0.0.118 - - [21/Feb/2024 15:29:24] "POST /cmd HTTP/1.1" 200 -
xiaomusic    | 10.0.0.118 - - [21/Feb/2024 15:29:24] "POST /cmd HTTP/1.1" 200 -
xiaomusic    | [BiliBili] BV1tw411X7Rr: Extracting videos in anthology
xiaomusic    | 10.0.0.118 - - [21/Feb/2024 15:29:24] "POST /cmd HTTP/1.1" 200 -
xiaomusic    | [BiliBili] 319943893: Extracting chapters
xiaomusic    | 10.0.0.118 - - [21/Feb/2024 15:29:24] "POST /cmd HTTP/1.1" 200 -
xiaomusic    | 10.0.0.118 - - [21/Feb/2024 15:29:24] "POST /cmd HTTP/1.1" 200 -
xiaomusic    | [BiliBili] Format(s) 1080P 高码率, 1080P 高清, 720P 高清, 4K 超清 are missing; you have to login or become premium member to download them. Use --cookies-from-browser or --cookies for the authentication. See  https://github.com/yt-dlp/yt-dlp/wiki/FAQ#how-do-i-pass-cookies-to-yt-dlp  for how to manually pass cookies
xiaomusic    | [info] BV1tw411X7Rr: Downloading 1 format(s): 30280
xiaomusic    | 10.0.0.118 - - [21/Feb/2024 15:29:24] "POST /cmd HTTP/1.1" 200 -
xiaomusic    | 10.0.0.118 - - [21/Feb/2024 15:29:24] "POST /cmd HTTP/1.1" 200 -
xiaomusic    | [download] Destination: music/安河桥北.m4a
[download]   0.3% of    5.61MiB at    6.24MiB/s ETA 00:0010.0.0.118 - - [21/Feb/2024 15:29:24] "POST /cmd HTTP/1.1" 200 -
[download]   0.5% of    5.61MiB at    2.42MiB/s ETA 00:0210.0.0.118 - - [21/Feb/2024 15:29:24] "POST /cmd HTTP/1.1" 200 -
[download]   1.1% of    5.61MiB at    1.50MiB/s ETA 00:0310.0.0.118 - - [21/Feb/2024 15:29:24] "POST /cmd HTTP/1.1" 200 -
[download]   4.4% of    5.61MiB at    2.35MiB/s ETA 00:0210.0.0.118 - - [21/Feb/2024 15:29:24] "POST /cmd HTTP/1.1" 200 -
[download]   8.9% of    5.61MiB at    3.59MiB/s ETA 00:0110.0.0.118 - - [21/Feb/2024 15:29:24] "POST /cmd HTTP/1.1" 200 -
[download] 100% of    5.61MiB in 00:00:00 at 10.83MiB/s  
xiaomusic    | [ExtractAudio] Destination: music/安河桥北.mp3
xiaomusic    | Deleting original file music/安河桥北.m4a (pass -k to keep)
xiaomusic    | [download] Finished downloading playlist: 安河桥北
xiaomusic    | [02/21/24 15:29:29] INFO     播放                               xiaomusic.py:461
xiaomusic    |                              http://10.0.0.4:8090/music/%E5%AE%                 
xiaomusic    |                              89%E6%B2%B3%E6%A1%A5%E5%8C%97.mp3                  
xiaomusic    |                     INFO     已经开始播放了                     xiaomusic.py:464
xiaomusic    |                     INFO     歌曲music/安河桥北.mp3的时长251秒  xiaomusic.py:371
xiaomusic    |                     INFO     251秒后将会播放下一首              xiaomusic.py:385
xiaomusic    |                     INFO     匹配到指令. opkey:set_volume#      xiaomusic.py:441
xiaomusic    |                              opvalue:set_volume oparg:24  
```

## 评论


### 评论 1 - hanxi

需要添加环境变量
```
environment:
    XIAOMUSIC_PORT:80
ports:
      - 80:80
 ```

---

### 评论 2 - newrookie001

> 需要添加环境变量
> 
> ```
> environment:
>     XIAOMUSIC_PORT:80
> ports:
>       - 80:80
> ```

自己走了点弯路，半天才搞明白。补充说明：
> environment:
>     XIAOMUSIC_PORT: 5678 #就是“5678”可以根据自己要求设置，但要求上下的5678都设置成一个
> ports:
>       - 5678:5678 

---

### 评论 3 - hanxi

如果换端口，需要3个数字一致，比如

```
environment:
    XIAOMUSIC_PORT:6874
ports:
      - 6874:6874
```

---

### 评论 4 - hanxi

文档类型的我都打开下，方便其他人看到。

---

### 评论 5 - flymin

docker-compose 中对应关系应该是
```yaml
ports:
    - aaaa:bbbb
environment:
    XIAOMUSIC_PORT: bbbb  # 配置文件中的 port，后台：监听端口(修改后需要重启)
    XIAOMUSIC_PUBLIC_PORT: aaaa # 配置文件中的 public_port，后台：外网访问端口(0表示跟监听端口一致)
```

以上，docker 环境中基本不存在需要修改 bbbb 的情况，也就是不用设置 XIAOMUSIC_PORT。如果需要修改端口，只需要修改两处 aaaa
如果使用反向代理，则转发 localhost:aaaa，XIAOMUSIC_PUBLIC_PORT 设置成代理的监听端口 cccc

另外，setting 文件存在会覆盖环境变量。启动过之后需要直接修改 settings.json 或者在后台修改

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/19)
