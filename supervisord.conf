[unix_http_server]
file=/var/run/supervisor.sock              ; Unix套接字文件路径
chmod=0777                                 ; 套接字权限

[supervisord]
pidfile=/tmp/supervisord.pid               ; PID文件路径
logfile=/app/supervisord.log               ; 日志文件路径
logfile_maxbytes=50MB                      ; 日志文件的最大大小
logfile_backups=3                          ; 保留的旧日志文件数量
loglevel=info                              ; 日志级别


[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock ; 使用Unix套接字与服务通信

[program:xiaomusic]
command=/app/.venv/bin/python3 /app/xiaomusic.py
directory=/app
autostart=true
autorestart=true
startretries=99999
startsecs=60
stderr_logfile=/app/xiaomusic.log.txt
stdout_logfile=/app/xiaomusic.log.txt
