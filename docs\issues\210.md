---
title: yt-dlp cookies 文件上传功能
---

# yt-dlp cookies 文件上传功能

此功能用于解决 yt-dlp 下载资源失败时使用，比如 ip 被 B站或者 youtube 加入黑名单后才需要使用。

上传的文件用于 yt-dlp 的 `--cookies` 参数。
```
--cookies FILE   Netscape formatted file to read cookies from
                      and dump cookie jar in
```

## 获取 cookies.txt 文件

1. 下载插件 [Get cookies.txt LOCALLY](https://chromewebstore.google.com/detail/cclelndahbckbenkjhflpdbgdldlbecc)
2. 给予插件访问权限和无痕模式允许使用
![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/89f6ce94-bb51-4805-8c16-a867ba41e5d2)
3. 打开无痕窗口
4. 打开 youtube.com
5. 登陆 youtube.com
6. 打开新标签页
7. 关闭 youtube.com 的标签页
8. 保存 cookies.txt
![image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/64242595-7b5c-4159-a8bc-4fc922d5de9e)

原因见 https://github.com/yt-dlp/yt-dlp/wiki/Extractors#exporting-youtube-cookies

## 上传 cookies.txt

1. 打开设置页面
2. 设置启用yt-dlp-cookies 选项为 true
![Screenshot_2024-09-29-22-31-40-134_com android chrome-edit](https://gproxy.hanxi.cc/proxy/user-attachments/assets/49760905-475b-493c-9ff4-271c5e797b2f)
3. 点击保存
4. 再点击选择文件，选择前面保存好的 cookies.txt 文件，点击上传。
![Screenshot_2024-09-29-22-33-21-361_com android chrome-edit](https://gproxy.hanxi.cc/proxy/user-attachments/assets/838bfd1c-f19f-4690-86b0-8208d596fbf1)


## 后续用途

1. 语音下载歌曲都会带上前面上传的 cookies.txt 文件去搜索下载歌曲。
2. 歌曲批量下载工具下载歌曲或者歌单时也会带上 cookies.txt 文件。



## 评论


### 评论 1 - kingfly2016

0.3.37的版本并没有发现可以开启yt-dlp-cookies 并上传cookies文件的地方,尝试把导出的cookies.txt手工上传到conf目录下,没有生效.
![屏幕截图_11-10-2024_183725_192 168 6 202](https://gproxy.hanxi.cc/proxy/user-attachments/assets/9b8b9750-b616-4fd3-8a3c-216b2f99d02c)


---

### 评论 2 - hanxi

需要等38版本，或者用测试版本，镜像名后面加 :main

---

### 评论 3 - kingfly2016



> 需要等38版本，或者用测试版本，镜像名后面加 :main

谢谢

---

### 评论 4 - sunmiao0301

20250117实测可用

---

### 评论 5 - Lonely-Sit

> 20250117实测可用

佬你今天还可以使用吗？我按照教程操作了但是下载不下来歌曲

---

### 评论 6 - sunmiao0301

> > 20250117实测可用
> 
> 佬你今天还可以使用吗？我按照教程操作了但是下载不下来歌曲

用cookie的方式可能会被封号，ytb限制的是ip，更好的办法是，切换一下梯子节点，多试几个。

---

### 评论 7 - Lonely-Sit

> > > 20250117实测可用
> > 
> > 
> > 大佬你今天还可以用吗？我按照教程操作了但是下载不下来歌曲
> 
> 用cookie限制的方式可能会被封号，ytb是ip，更好的办法是，切换一下梯子节点，多试几个。

我使用相同ip在电脑端下载歌曲就没有问题 我无法理解

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/210)
