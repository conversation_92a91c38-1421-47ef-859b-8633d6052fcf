---
title: 【插件】自定义口令功能
---

# 【插件】自定义口令功能

自定义口令配置需要配置到 config.json 文件里，使用 config.json 方式启动。参考 </issues/94.html> 。

口令的配置方式见 config-example.json 文件。口令对应的代码需要写到 `plugins/` 目录下面，如果是容器启动，则需要把这个目录挂载出来。

config.json 格式是下面这样的。

```json
{
    "hardware": "L07A",
    "account": "",
    "password": "",
    "mi_did": "",
    "cookie": "",
    "verbose": false,
    "music_path": "music",
    "conf_path": null,
    "hostname": "***********",
    "port": 8090,
    "public_port": 0,
    "proxy": null,
    "search_prefix": "bilisearch:",
    "ffmpeg_location": "./ffmpeg/bin",
    "active_cmd": "play,random_play,playlocal,play_music_list,stop",
    "exclude_dirs": "@eaDir",
    "music_path_depth": 10,
    "disable_httpauth": true,
    "httpauth_username": "admin",
    "httpauth_password": "admin",
    "music_list_url": "",
    "music_list_json": "",
    "disable_download": false,
    "key_word_dict": {
        "播放歌曲": "play",
        "播放本地歌曲": "playlocal",
        "关机": "stop",
        "下一首": "play_next",
        "单曲循环": "set_play_type_one",
        "全部循环": "set_play_type_all",
        "随机播放": "random_play",
        "分钟后关机": "stop_after_minute",
        "播放列表": "play_music_list",
        "刷新列表": "gen_music_list",
        "set_volume#": "set_volume",
        "get_volume#": "get_volume",
        "本地播放歌曲": "playlocal",
        "放歌曲": "play",
        "暂停": "stop",
        "停止": "stop",
        "停止播放": "stop",
        "测试自定义口令": "exec#code1(\"hello\")",
        "测试链接": "exec#httpget(\"https://github.com/hanxi/xiaomusic\")"
    },
    "key_match_order": [
        "set_volume#",
        "get_volume#",
        "分钟后关机",
        "播放歌曲",
        "下一首",
        "单曲循环",
        "全部循环",
        "随机播放",
        "关机",
        "刷新列表",
        "播放列表",
        "播放本地歌曲",
        "本地播放歌曲",
        "放歌曲",
        "暂停",
        "停止",
        "停止播放",
        "测试自定义口令",
        "测试链接"
    ],
    "use_music_api": false,
    "use_music_audio_id": "1582971365183456177",
    "use_music_id": "355454500",
    "log_file": "/tmp/xiaomusic.txt",
    "fuzzy_match_cutoff": 0.6,
    "enable_fuzzy_match": true,
    "stop_tts_msg": "收到,再见",
    "keywords_playlocal": "播放本地歌曲,本地播放歌曲",
    "keywords_play": "播放歌曲,放歌曲",
    "keywords_stop": "关机,暂停,停止,停止播放",
    "user_key_word_dict": {
        "测试自定义口令": "exec#code1(\"hello\")",
        "测试链接": "exec#httpget(\"https://github.com/hanxi/xiaomusic\")"
    }
}
```

配置自定义口令时，只需要配置 user_key_word_dict 即可，会自动插入到 key_word_dict 里的。配置格式是：

```
 "测试自定义口令": "exec#code1(\"hello\")",
```

其中 "测试自定义口令" 就是对小爱音箱说的，`"exec#code1(\"hello\")"` 就是要执行的插件代码，代码以 `exec#` 开头，后面紧跟着执行代码。这里 code1 是一个插件函数，插件函数需要在 plugin 目录里实现，一个文件只会导出一个与文件名相同的插件函数。所以 code1 函数是在 plugin/code1.py 里实现的。

```
async def code1(arg1):
    global log, xiaomusic
    log.info(f"code1:{arg1}")
    await xiaomusic.do_tts("你好，我是自定义的测试口令")
```

这里只是演示了打印日志和让小爱音箱说话。还有一个示例插件是 httpget ，可以用来访问 url 。

比如下面这样配置的话，当对小爱音箱说测试链接时，会去访问 url ，可以用来很多其他的事情。

```
"测试链接": "exec#httpget(\"https://github.com/hanxi/xiaomusic\")
```

最后还需要在 `active_cmd` 中配上口令用于唤醒：

```
  "active_cmd": "play,set_random_play,playlocal,play_music_list,play_music_list_index,stop_after_minute,stop,测试自定义口令",
```

感兴趣的可以体验一下，写了有什么好玩的插件也可以在这里分享，或者提 pr 合并进官方库里作为自带插件。


## 评论


### 评论 1 - carson512

如果开启服务的状态下 如何唤醒才能调用原有的播放QQ 网易云？而特定唤醒词调用xiaoai播放？

---

### 评论 2 - hanxi

> 如果开启服务的状态下 如何唤醒才能调用原有的播放QQ 网易云？而特定唤醒词调用xiaoai播放？

不使用 xiaomusic 的唤醒词就会调用音箱自带的，比如说播放音乐

---

### 评论 3 - shellingford37

```
[23:26:12] [0.3.30] [INFO] xiaomusic.py:531: 收到消息:测试自定义口令 控制面板:False did:290874427
[23:26:12] [0.3.30] [INFO] xiaomusic.py:577: 完全匹配指令. query:测试自定义口令 opvalue:exec#code1("hello")
[23:26:12] [0.3.30] [INFO] code1.py:3: code1:hello
[23:26:12] [0.3.30] [ERROR] xiaomusic.py:542: Execption XiaoMusic.do_tts() missing 1 required positional argument: 'value'
Traceback (most recent call last):
  File "/app/xiaomusic/xiaomusic.py", line 540, in do_check_cmd
    await func(did=did, arg1=oparg)
  File "/app/xiaomusic/xiaomusic.py", line 890, in exec
    await self.plugin_manager.execute_plugin(code)
  File "/app/xiaomusic/plugin.py", line 66, in execute_plugin
    await coroutine
  File "/app/plugins/code1.py", line 4, in code1
    await xiaomusic.do_tts("你好，我是自定义的测试口令")
TypeError: XiaoMusic.do_tts() missing 1 required positional argument: 'value'
```

我用code1的代码执行报错，有大佬知道为什么吗？

---

### 评论 4 - hanxi

@shellingford37 重构后漏改了，修复了。

---

### 评论 5 - guoxiangke

先说播放歌曲，再说 测试自定义口令 就行 

---

### 评论 6 - CZJCC

想请教下插件那个功能，如何把用户的语音输入作为参数内容传到自定义函数里

---

### 评论 7 - hanxi

> 想请教下插件那个功能，如何把用户的语音输入作为参数内容传到自定义函数里

现在获取不到，等我加个接口获取吧。

---

### 评论 8 - CZJCC

666,支持以后我可以贡献一个接入通义模型的插件

---

### 评论 9 - hanxi

@CZJCC 你可以更新看看 plugins/code1.py 的测试代码，我测试了是可以拿到语音输入的原始内容的。

---

### 评论 10 - hanxi

文档更新了下，active_cmd 也需要配置一下才能正常唤醒。

---

### 评论 11 - CZJCC

> @CZJCC 你可以更新看看 plugins/code1.py 的测试代码，我测试了是可以拿到语音输入的原始内容的。

我原先设想的事用户的话术是”通义提问为什么地球是圆的“，指令匹配的时候通义提问前缀匹配到类似于code1方法，为什么地球是圆的作为参数传入这个函数，但我看现在是拿历史记录实现的

---

### 评论 12 - hanxi

是的，插件函数里面再切割一下前缀就行。last_record就是当前的那条语音数据。

---

### 评论 13 - hanxi

> > @CZJCC 你可以更新看看 plugins/code1.py 的测试代码，我测试了是可以拿到语音输入的原始内容的。
> 
> 我原先设想的事用户的话术是”通义提问为什么地球是圆的“，指令匹配的时候通义提问前缀匹配到类似于code1方法，为什么地球是圆的作为参数传入这个函数，但我看现在是拿历史记录实现的

是的，这样比较简单，交给插件里面处理也比较自由。

---

### 评论 14 - mogeqian

key_word_dict中的“播放歌曲”口令是不能修改的是吧？因为以前用小爱同学播放歌曲说习惯了，总是触发xiaomusic自动下载歌曲，我想把口令改成“查找歌曲”，这样我当说播放歌曲的时候就调用网易云音乐或者QQ音乐，当我说查找歌曲的时候就先看本地有没有歌曲，没有话就自动下载到本地。
当我先按照config-example.json的模板写好如下配置并重命名为config.json
```
  "key_word_dict": {
    "查找歌曲": "play",
    "播放本地歌曲": "playlocal",
    "关机": "stop",
    "下一首": "play_next",
    "单曲循环": "set_play_type_one",
    "全部循环": "set_play_type_all",
    "随机播放": "set_random_play",
    "分钟后关机": "stop_after_minute",
    "播放列表": "play_music_list",
    "刷新列表": "gen_music_list",
    "本地播放歌曲": "playlocal",
    "下载歌曲": "play",
    "暂停": "stop",
    "停止": "stop",
    "停止播放": "stop",
    "测试自定义口令": "exec#code1(\"hello\")",
    "测试链接": "exec#httpget(\"https://github.com/hanxi/xiaomusic\")"
  },
  "key_match_order": [
    "分钟后关机",
    "查找歌曲",
    "下一首",
    "单曲循环",
    "全部循环",
    "随机播放",
    "关机",
    "刷新列表",
    "播放列表",
    "播放本地歌曲",
    "本地播放歌曲",
    "下载歌曲",
    "暂停",
    "停止",
    "停止播放",
    "测试自定义口令",
    "测试链接"
  ],
```

用以下命令安装docker
`docker run --name xiaomusic -p 5488:5488 -v /mnt/sharedata/audiodata/musci/xiaomusic:/app/music -v /mnt/data_sdb1/docker/xiaomusic/config.json:/app/config.json -e XIAOMUSIC_PORT=5488 hanxi/xiaomusic --config /app/config.json`
日志里提示的依然是：

` key_word_dict={'播放歌曲': 'play', '播放本地歌曲': 'playlocal', '关机': 'stop', '下一首': 'play_next', '上一首': 'play_prev', '单曲循环': 'set_play_type_one', '全部循环': 'set_play_type_all', '随机播放': 'set_random_play', '分钟后关机': 'stop_after_minute', '播放列表': 'play_music_list', '刷新列表': 'gen_music_list', '加入收藏': 'add_to_favorites', '收藏歌曲': 'add_to_favorites', '取消收藏': 'del_from_favorites', '播放列表第': 'play_music_list_index', '本地播放歌曲': 'playlocal', '查找歌曲': 'play', '下载歌曲': 'play', '暂停': 'stop', '停止': 'stop', '停止播放': 'stop', '播放歌单': 'play_music_list', '测试自定义口令': 'exec#code1("hello")', '测试链接': 'exec#httpget("https://github.com/hanxi/xiaomusic")'}, key_match_order=['分钟后关机', '播放歌曲', '下一首', '上一首', '单曲循环', '全部循环', '随机播放', '关机', '刷新列表', '播放列表第', '播放列表', '加入收藏', '收藏歌曲', '取消收藏', '播放本地歌曲', '本地播放歌曲', '查找歌曲', '下载歌曲', '暂停', '停止', '停止播放', '播放歌单', '测试自定义口令', '测试链接']`

似乎自定义的口令只能以插入的方式添加上去，并不能替换掉原来的口令




---

### 评论 15 - hanxi

> key_word_dict中的“播放歌曲”口令是不能修改的是吧？因为以前用小爱同学播放歌曲说习惯了，总是触发xiaomusic自动下载歌曲，我想把口令改成“查找歌曲”，这样我当说播放歌曲的时候就调用网易云音乐或者QQ音乐，当我说查找歌曲的时候就先看本地有没有歌曲，没有话就自动下载到本地。 当我先按照config-example.json的模板写好如下配置并重命名为config.json
> 
> ```
>   "key_word_dict": {
>     "查找歌曲": "play",
>     "播放本地歌曲": "playlocal",
>     "关机": "stop",
>     "下一首": "play_next",
>     "单曲循环": "set_play_type_one",
>     "全部循环": "set_play_type_all",
>     "随机播放": "set_random_play",
>     "分钟后关机": "stop_after_minute",
>     "播放列表": "play_music_list",
>     "刷新列表": "gen_music_list",
>     "本地播放歌曲": "playlocal",
>     "下载歌曲": "play",
>     "暂停": "stop",
>     "停止": "stop",
>     "停止播放": "stop",
>     "测试自定义口令": "exec#code1(\"hello\")",
>     "测试链接": "exec#httpget(\"https://github.com/hanxi/xiaomusic\")"
>   },
>   "key_match_order": [
>     "分钟后关机",
>     "查找歌曲",
>     "下一首",
>     "单曲循环",
>     "全部循环",
>     "随机播放",
>     "关机",
>     "刷新列表",
>     "播放列表",
>     "播放本地歌曲",
>     "本地播放歌曲",
>     "下载歌曲",
>     "暂停",
>     "停止",
>     "停止播放",
>     "测试自定义口令",
>     "测试链接"
>   ],
> ```
> 
> 用以下命令安装docker `docker run --name xiaomusic -p 5488:5488 -v /mnt/sharedata/audiodata/musci/xiaomusic:/app/music -v /mnt/data_sdb1/docker/xiaomusic/config.json:/app/config.json -e XIAOMUSIC_PORT=5488 hanxi/xiaomusic --config /app/config.json` 日志里提示的依然是：
> 
> ` key_word_dict={'播放歌曲': 'play', '播放本地歌曲': 'playlocal', '关机': 'stop', '下一首': 'play_next', '上一首': 'play_prev', '单曲循环': 'set_play_type_one', '全部循环': 'set_play_type_all', '随机播放': 'set_random_play', '分钟后关机': 'stop_after_minute', '播放列表': 'play_music_list', '刷新列表': 'gen_music_list', '加入收藏': 'add_to_favorites', '收藏歌曲': 'add_to_favorites', '取消收藏': 'del_from_favorites', '播放列表第': 'play_music_list_index', '本地播放歌曲': 'playlocal', '查找歌曲': 'play', '下载歌曲': 'play', '暂停': 'stop', '停止': 'stop', '停止播放': 'stop', '播放歌单': 'play_music_list', '测试自定义口令': 'exec#code1("hello")', '测试链接': 'exec#httpget("https://github.com/hanxi/xiaomusic")'}, key_match_order=['分钟后关机', '播放歌曲', '下一首', '上一首', '单曲循环', '全部循环', '随机播放', '关机', '刷新列表', '播放列表第', '播放列表', '加入收藏', '收藏歌曲', '取消收藏', '播放本地歌曲', '本地播放歌曲', '查找歌曲', '下载歌曲', '暂停', '停止', '停止播放', '播放歌单', '测试自定义口令', '测试链接']`
> 
> 似乎自定义的口令只能以插入的方式添加上去，并不能替换掉原来的口令

可以在网页后台设置页面改。

---

### 评论 16 - mogeqian

不行，后台设置如图
![QQ截图**************](https://gproxy.hanxi.cc/proxy/user-attachments/assets/cc89512f-cab9-488d-b0d6-5b2a3a720ac2)
日志如下：
```
[2024-11-11 18:08:04] [0.3.46] [INFO] xiaomusic.py:1130: update_config_from_setting ok. data:Config(account='**', password='**', mi_did='*********,*********', miio_tts_command='', cookie='', verbose=False, music_path='music', download_path='music/download', conf_path='conf', cache_dir='cache', hostname='************', port=8090, public_port=0, proxy='', search_prefix='bilisearch:', ffmpeg_location='./ffmpeg/bin', active_cmd='play,set_random_play,playlocal,play_music_list,stop', exclude_dirs='@eaDir', music_path_depth=10, disable_httpauth=True, httpauth_username='******', httpauth_password='******', music_list_url='', music_list_json='', custom_play_list_json='', disable_download=False, key_word_dict={'播放歌曲': 'play', '播放本地歌曲': 'playlocal', '关机': 'stop', '下一首': 'play_next', '上一首': 'play_prev', '单曲循环': 'set_play_type_one', '全部循环': 'set_play_type_all', '随机播放': 'set_random_play', '分钟后关机': 'stop_after_minute', '播放列表': 'play_music_list', '刷新列表': 'gen_music_list', '加入收藏': 'add_to_favorites', '收藏歌曲': 'add_to_favorites', '取消收藏': 'del_from_favorites', '播放列表第': 'play_music_list_index', '本地播放歌曲': 'playlocal', '查找歌曲': 'play', '下载歌曲': 'play', '暂停': 'stop', '停止': 'stop', '停止播放': 'stop', '播放歌单': 'play_music_list', '测试自定义口令': 'exec#code1("hello")', '测试链接': 'exec#httpget("https://github.com/hanxi/xiaomusic")'}, key_match_order=['分钟后关机', '播放歌曲', '下一首', '上一首', '单曲循环', '全部循环', '随机播放', '关机', '刷新列表', '播放列表第', '播放列表', '加入收藏', '收藏歌曲', '取消收藏', '播放本地歌曲', '本地播放歌曲', '查找歌曲', '下载歌曲', '暂停', '停止', '停止播放', '播放歌单', '测试自定义口令', '测试链接'], use_music_api=False, use_music_audio_id='1582971365183456177', use_music_id='355454500', log_file='/tmp/xiaomusic.txt', fuzzy_match_cutoff=0.6, enable_fuzzy_match=True, stop_tts_msg='收到,再见', enable_config_example=False, keywords_playlocal='播放本地歌曲,本地播放歌曲', keywords_play='查找歌曲,下载歌曲', keywords_stop='关机,暂停,停止,停止播放', keywords_playlist='播放列表,播放歌单', user_key_word_dict={'测试自定义口令': 'exec#code1("hello")', '测试链接': 'exec#httpget("https://github.com/hanxi/xiaomusic")'}, enable_force_stop=False, devices={'*********': Device(did='*********', device_id='******', hardware='LX06', name='小爱音箱Pro', play_type='', cur_music='', cur_playlist='全部'), '*********': Device(did='*********', device_id='*********', hardware='L15A', name='小米AI音箱(第二代)', play_type='', cur_music='', cur_playlist='全部')}, group_list='', remove_id3tag=False, convert_to_mp3=False, delay_sec=3, continue_play=False, pull_ask_sec=1, crontab_json='', enable_yt_dlp_cookies=False, get_ask_by_mina=False)
[2024-11-11 18:08:04] [0.3.46] [INFO] xiaomusic.py:1133: 语音控制已启动, 用【分钟后关机/播放歌曲/下一首/上一首/单曲循环/全部循环/随机播放/关机/刷新列表/播放列表第/播放列表/加入收藏/收藏歌曲/取消收藏/播放本地歌曲/本地播放歌曲/查找歌曲/下载歌曲/暂停/停止/停止播放/播放歌单/测试自定义口令/测试链接】开头来控制
[2024-11-11 18:08:04] [0.3.46] [INFO] xiaomusic.py:543: 协程时间循环未启动
[2024-11-11 18:08:04] [0.3.46] [INFO] xiaomusic.py:1250: 没打乱 全部 ['乌兰托娅 火红的萨日朗', '凤凰传奇麝香夫人'] ... ['罗大佑童年', '阿嬷'] with len: 7
[2024-11-11 18:08:04] [0.3.46] [INFO] xiaomusic.py:1250: 没打乱 全部 ['乌兰托娅 火红的萨日朗', '凤凰传奇麝香夫人'] ... ['罗大佑童年', '阿嬷'] with len: 7
[2024-11-11 18:08:04] [0.3.46] [INFO] analytics.py:28: analytics init ok
[2024-11-11 18:08:04] [0.3.46] [INFO] xiaomusic.py:104: Startup OK. Config(account='***', password='***', mi_did='*********,*********', miio_tts_command='', cookie='', verbose=False, music_path='music', download_path='music/download', conf_path='conf', cache_dir='cache', hostname='************', port=8090, public_port=0, proxy='', search_prefix='bilisearch:', ffmpeg_location='./ffmpeg/bin', active_cmd='play,set_random_play,playlocal,play_music_list,stop', exclude_dirs='@eaDir', music_path_depth=10, disable_httpauth=True, httpauth_username='******', httpauth_password='******', music_list_url='', music_list_json='', custom_play_list_json='', disable_download=False, key_word_dict={'播放歌曲': 'play', '播放本地歌曲': 'playlocal', '关机': 'stop', '下一首': 'play_next', '上一首': 'play_prev', '单曲循环': 'set_play_type_one', '全部循环': 'set_play_type_all', '随机播放': 'set_random_play', '分钟后关机': 'stop_after_minute', '播放列表': 'play_music_list', '刷新列表': 'gen_music_list', '加入收藏': 'add_to_favorites', '收藏歌曲': 'add_to_favorites', '取消收藏': 'del_from_favorites', '播放列表第': 'play_music_list_index', '本地播放歌曲': 'playlocal', '查找歌曲': 'play', '下载歌曲': 'play', '暂停': 'stop', '停止': 'stop', '停止播放': 'stop', '播放歌单': 'play_music_list', '测试自定义口令': 'exec#code1("hello")', '测试链接': 'exec#httpget("https://github.com/hanxi/xiaomusic")'}, key_match_order=['分钟后关机', '播放歌曲', '下一首', '上一首', '单曲循环', '全部循环', '随机播放', '关机', '刷新列表', '播放列表第', '播放列表', '加入收藏', '收藏歌曲', '取消收藏', '播放本地歌曲', '本地播放歌曲', '查找歌曲', '下载歌曲', '暂停', '停止', '停止播放', '播放歌单', '测试自定义口令', '测试链接'], use_music_api=False, use_music_audio_id='1582971365183456177', use_music_id='355454500', log_file='/tmp/xiaomusic.txt', fuzzy_match_cutoff=0.6, enable_fuzzy_match=True, stop_tts_msg='收到,再见', enable_config_example=False, keywords_playlocal='播放本地歌曲,本地播放歌曲', keywords_play='查找歌曲,下载歌曲', keywords_stop='关机,暂停,停止,停止播放', keywords_playlist='播放列表,播放歌单', user_key_word_dict={'测试自定义口令': 'exec#code1("hello")', '测试链接': 'exec#httpget("https://github.com/hanxi/xiaomusic")'}, enable_force_stop=False, devices={'*********': Device(did='*********', device_id='*****', hardware='LX06', name='小爱音箱Pro', play_type='', cur_music='', cur_playlist='全部'), '*********': Device(did='*********', device_id='*********', hardware='L15A', name='小米AI音箱(第二代)', play_type='', cur_music='', cur_playlist='全部')}, group_list='', remove_id3tag=False, convert_to_mp3=False, delay_sec=3, continue_play=False, pull_ask_sec=1, crontab_json='', enable_yt_dlp_cookies=False, get_ask_by_mina=False)
[2024-11-11 18:08:04] [0.3.46] [INFO] httpserver.py:111: disable_httpauth:True
[18:08:04] [0.3.46] [INFO] Started server process [1]
[18:08:04] [0.3.46] [INFO] Waiting for application startup.
[2024-11-11 18:08:04] [0.3.46] [INFO] xiaomusic.py:541: 启动后台构建 tag cache
[18:08:04] [0.3.46] [INFO] Application startup complete.
[2024-11-11 18:08:04] [0.3.46] [INFO] xiaomusic.py:513: 已从【cache/tag_cache.json】加载 tag cache
[18:08:04] [0.3.46] [INFO] Uvicorn running on http://['0.0.0.0', '::']:8090 (Press CTRL+C to quit)
[2024-11-11 18:08:04] [0.3.46] [INFO] xiaomusic.py:527: 保存：tag cache 已保存到【cache/tag_cache.json】
[2024-11-11 18:08:04] [0.3.46] [INFO] xiaomusic.py:577: tag 更新完成
[2024-11-11 18:08:04] [0.3.46] [INFO] xiaomusic.py:248: 选中的设备: {'*********': Device(did='*********', device_id='******', hardware='LX06', name='小爱音箱Pro', play_type='', cur_music='', cur_playlist='全部'), '*********': Device(did='*********', device_id='*********', hardware='L15A', name='小米AI音箱(第二代)', play_type='', cur_music='', cur_playlist='全部')}
[18:08:34] [0.3.46] [INFO] 172.20.0.1:35058 - "GET /static/default/setting.html HTTP/1.1" 304
[18:08:34] [0.3.46] [INFO] 172.20.0.1:35058 - "GET /getversion HTTP/1.1" 200
```
使用的docker-compose命令安装
services:
  xiaomusic:
    image: hanxi/xiaomusic
    container_name: xiaomusic
    restart: unless-stopped
    ports:
      - 8090:8090
    volumes:
      - /mnt/sharedata/audiodata/musci/xiaomusic:/app/music
      - /mnt/data_sdb1/docker/xiaomusic/config.json:/app/config.json
    command: ['--config', '/app/config.json']

根据日志的提示，'播放歌曲': 'play'依然存在，只是增加了 '查找歌曲': 'play', '下载歌曲': 'play', 这两个关于play的自定义口令，所以实际上play有三条口令 “播放歌曲、查找歌曲、下载歌曲”，能否删除掉'播放歌曲': 'play'这个系统默认的口令？只使用 '查找歌曲': 'play', '下载歌曲': 'play', 这两个关于play的自定义口令

---

### 评论 17 - hanxi

@mogeqian 另外提个 issue 吧，现在应该是不支持删除默认的口令。

---

### 评论 18 - mogeqian

好的，已经重开了一个issue  #259 

---

### 评论 19 - wjcroom

@hanxi 你好大神 , 自定义口令基本搞定了,还有些不确定对不对
在项目命令中只有停止,没有继续. 
能把stop.做成, 乒乓吗.继续. 暂停的切换.pause play
 "active_cmd": "play,set_random_play,playlocal,play_music_list,play_music_list_index,stop_after_minute,stop,测试自定义口令",
这个激活cmd是什么作用呢, 怎么看着默认的是函数,只有最后一个是中文呢? 
 
已经把自定义3thplay音量加入了. 其他的大点声小点声, 大点音,小点音.,还有好多.
好的好像是激活xiaomusic的 但是为啥前面的是单词?
config.json,   已经被setting.json替换了的. 

```
async def set_myvolume(self, did="", arg1=0, **kwargs):
        if did not in self.devices:
            self.log.info(f"设备 did:{did} 不存在, 不能设置音量")
            return
        if arg1=="up":
                await self.devices[did].thdplay('up')
        elif arg1=="down":
            await self.devices[did].thdplay('dow')
            
        else:
          volume =  chinese_to_number(arg1)    
          await self.devices[did].thdplay('volume',str(volume))

```

---

### 评论 20 - hanxi

激活cmd支持配置 key 和 value，用于决定是否能在 xiaomusic 不播放的时候能否唤醒 xiaomusic 。

config.json 是基本废弃了，作为一个初始化配置存在。

暂停不方便做，没有好的接口控制音箱暂停，所以就没加这个口令。

---

### 评论 21 - wjcroom

> 激活cmd支持配置 key 和 value，用于决定是否能在 xiaomusic 不播放的时候能否唤醒 xiaomusic 。
> 
> config.json 是基本废弃了，作为一个初始化配置存在。
> 
> 暂停不方便做，没有好的接口控制音箱暂停，所以就没加这个口令。
1.现在有了停止，我觉得继续可以是重启最近一首歌，在停止前把正在播放存放在某处。存  stop 里的name可以吗。感觉会有重名呢？

2. 有没有可能让小爱在技能中心增加自己的【小音乐】技能，进入xiaomusic对话模式，直到退出。这样不再互相干扰。  


---

### 评论 22 - hanxi

1. 目前会记录最后一次播放的歌曲，正常说播放歌曲，不带歌曲名字应该就会重头播放的。
2. 对话模式有些设备不支持，实现也比较复杂，xiaogpt那些项目是支持对话模式的，我就懒得支持了。

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/105)
