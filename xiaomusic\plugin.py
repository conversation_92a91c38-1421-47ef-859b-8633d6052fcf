import importlib
import inspect
import pkgutil
import abc
from plugins import Plugin

def is_abstract_class(cls):
    """
    全面检测类是否为抽象类，支持：
    1. Python 标准 abc 模块定义的抽象类
    2. 包含 @abstractmethod 的类
    3. 继承自抽象类但未实现全部方法的类
    """
    # 非类对象直接返回 False
    if not inspect.isclass(cls):
        return False

    # 检查 ABC 元类
    if isinstance(cls, abc.ABCMeta):
        return bool(cls.__abstractmethods__)

    # 检查自定义元类
    if hasattr(cls, "__abstractmethods__") and cls.__abstractmethods__:
        return True

    # 检查类中是否有抽象方法
    for attr in dir(cls):
        member = getattr(cls, attr)
        if getattr(member, "__isabstractmethod__", False):
            return True

    # 检查 MRO 链中是否存在未实现的抽象方法
    for base in cls.__mro__:
        if base is cls: continue
        base_abstracts = getattr(base, "__abstractmethods__", None)
        if base_abstracts and any(
            getattr(cls, method, None) is getattr(base, method, None)
            for method in base_abstracts
        ):
            return True

    return False

class PluginManager:
    def __init__(self, xiaomusic, plugin_dir="plugins"):
        self.xiaomusic = xiaomusic
        self.log = xiaomusic.log
        self._funcs = {}
        self._plugins = {}
        self._load_plugins(plugin_dir)

    def _load_plugins(self, plugin_dir):
        # 假设 plugins 已经在搜索路径上
        package_name = plugin_dir
        package = importlib.import_module(package_name)

        # 遍历 package 中所有模块并动态导入它们
        for _, modname, _ in pkgutil.iter_modules(package.__path__, package_name + "."):
            # 跳过__init__文件
            if modname.endswith("__init__"):
                module = importlib.import_module(modname)
                module.log = self.log
                module.xiaomusic = self.xiaomusic
                continue
            module = importlib.import_module(modname)

            ok = False
             # 检查模块中的所有类
            for k, cls in inspect.getmembers(module, inspect.isclass):
                # 验证是否为 Plugin 的子类且非基类自身
                if issubclass(cls, Plugin) and not inspect.isabstract(cls):
                    ok = True
                    # 将 log 和 xiaomusic 注入模块的命名空间
                    module.log = self.log
                    module.xiaomusic = self.xiaomusic

                    self.log.info(f"加载插件 {k}")
                    self._plugins[k] = cls()

            if not ok:
                del module

    def get_func(self, plugin_name):
        """根据插件名获取插件函数"""
        return self._funcs.get(plugin_name)

    def get_local_namespace(self):
        """返回包含所有插件函数的字典，可以用作 exec 要执行的代码的命名空间"""
        return self._funcs.copy()

    async def execute_plugin(self, code):
        """
        执行指定的插件代码。插件函数可以是同步或异步。
        :param code: 需要执行的插件函数代码（例如 'plugin1("hello")'）
        """
        # 分解代码字符串以获取函数名
        func_name = code.split("(")[0]

        # 根据解析出的函数名从插件字典中获取函数
        plugin_func = self.get_func(func_name)

        if not plugin_func:
            raise ValueError(f"No plugin function named '{func_name}' found.")

        # 检查函数是否是异步函数
        global_namespace = globals().copy()
        local_namespace = self.get_local_namespace()
        if inspect.iscoroutinefunction(plugin_func):
            # 如果是异步函数，构建执行用的协程对象
            coroutine = eval(code, global_namespace, local_namespace)
            # 等待协程执行
            await coroutine
        else:
            # 如果是普通函数，直接执行代码
            eval(code, global_namespace, local_namespace)


    async def check_full_match_cmd(self, did: str, query: str, ctrl_panel: bool = False) -> bool:
        for k in self._plugins:
            if self._plugins[k].check_full_match_cmd(did, query, ctrl_panel):
                return True
        return False

    async def check_pattern_match_cmd(self, did: str, query: str, ctrl_panel: bool = False) -> bool:
        for k in self._plugins:
            if self._plugins[k].check_pattern_match_cmd(did, query, ctrl_panel):
                return True
        return False
