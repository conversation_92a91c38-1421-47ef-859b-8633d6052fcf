<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <link rel="manifest" href="/static/manifest.json" />
  <link rel="icon" href="/static/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>小爱音箱操控面板</title>

  <!-- Google tag (gtag.js) -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-Z09NC1K7ZW"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() {
      dataLayer.push(arguments);
    }
    gtag("js", new Date());
    gtag("config", "G-Z09NC1K7ZW");
  </script>

  <!-- umami -->
  <script async defer src="https://umami.hanxi.cc/script.js" data-website-id="7bfb0890-4115-4260-8892-b391513e7e99"></script>

  <script type="text/javascript">
    if (navigator.serviceWorker != null) {
      navigator.serviceWorker
        .register("/static/sw.js")
        .then(function (registration) {
          console.log("Registered events at scope: ", registration.scope);
        });
    }
  </script>
</head>

<body>
  <div class="container_wrapper">
    <div class="logo">
      <img src="/static/xiaoai.png" alt="" />
    </div>
    <div class="desc">
      <h1>谁家灯火夜通明</h1>
      <p class="call">小爱同学？</p>
      <p class="answer">哎，我在</p>
    </div>
    <div class="options">
      <!-- 选择主题 /static/[theme] -->
      <div class="options_list">
        <a href="/static/default/index.html">默认主题</a>
      </div>
      <div class="options_list">
        <a href="/static/default_past/index.html">怀旧主题</a>
      </div>
      <div class="options_list">
        <a href="/static/tailwind/index.html">Tailwind</a>
      </div>
      <div class="options_list">
        <a href="/static/pure/index.html">Pure主题</a>
      </div>
      <div class="options_list">
        <a href="/static/xplayer/index.html">XMusicPlayer</a>
      </div>
      <div class="options_list weapp">
        <a href="https://github.com/F-loat/xiaoplayer" target="_blank">微信小程序</a>
        <iframe width="240px" height="240px" src="/static/weapp/qrcode.html"></iframe>
      </div>
      <div class="options_list">
        <a href="https://afdian.com/a/imhanxi" target="_blank">爱发电</a>
      </div>
      <div class="options_list">
        <a href="https://github.com/hanxi/xiaomusic" target="_blank">GitHub</a>
      </div>
      <div class="options_list">
        <a href="https://github.com/hanxi/xiaomusic/issues/211" target="_blank">帮助</a>
      </div>
    </div>
  </div>
  <footer>
    Powered by <a href="https://xdocs.hanxi.cc">XiaoMusic</a>
  </footer>
  <style>
    @font-face {
      font-family: "得意黑 斜体";
      font-weight: 400;
      src: url("//at.alicdn.com/wf/webfont/603VmyqiyGMz/gJk2ny0v51vn.woff2") format("woff2"),
        url("//at.alicdn.com/wf/webfont/603VmyqiyGMz/e2C1wSBHH86h.woff") format("woff");
      font-display: swap;
    }

    @font-face {
      font-family: "阿里妈妈数黑体 Bold";
      font-weight: 700;
      src: url("//at.alicdn.com/wf/webfont/603VmyqiyGMz/4DWYdFK3dz5J.woff2") format("woff2"),
        url("//at.alicdn.com/wf/webfont/603VmyqiyGMz/V7EBEKlNSdxC.woff") format("woff");
      font-display: swap;
    }

    body {
      background-color: rgb(47, 44, 67);
      height: 100%;
      overflow: hidden;
    }

    .container_wrapper {
      display: flex;
      justify-content: space-around;
      align-items: center;
      flex-wrap: wrap;
      height: 90vh;
      cursor: default;
    }

    h1 {
      font-weight: bold;
      color: #a2a9af;
      max-width: 600px;
      font-family: "得意黑 斜体", "Lucida Sans", "Lucida Sans Regular",
        "Lucida Grande", "Lucida Sans Unicode", Geneva, Verdana, sans-serif;
      font-size: 2.5em;
      border-bottom: 1px solid #a2a9af;
    }

    .container_wrapper .logo img {
      width: 140px;
      height: auto;
      filter: drop-shadow(10px 10px 10px rgba(0, 0, 0, 0.5));
    }

    .desc {
      text-align: center;
      color: #fff;
      margin: auto 30px;
      backdrop-filter: blur(5px);
    }

    .desc p {
      font-size: 1.2em;
      margin: 0;
      padding: 0;
      font-family: "阿里妈妈数黑体 Bold";
      font-weight: 800;
    }

    p.call {
      letter-spacing: 0.4em;
      font-size: 2.2em;
      line-height: 1.5;
      font-style: normal;
    }

    p.answer {
      letter-spacing: 0.23em;
      line-height: 1.5;
      font-style: normal;
      color: #a2a9af;
      margin-top: 10px;
    }

    .desc p::before,
    .desc p::after {
      font-family: "Lucida Sans", "Lucida Sans Regular", "Lucida Grande",
        "Lucida Sans Unicode", Geneva, Verdana, sans-serif;
      font-size: 1.5em;
      color: #4c5870;
    }

    .desc p::before {
      content: "“";
    }

    .desc p::after {
      content: "”";
    }

    .options {
      display: flex;
      flex-direction: column;
      user-select: none;
    }

    .options .options_list {
      font-size: 1.1em;
      position: relative;
      margin: 5px 2px;
      border: 1px solid #a2a9af;
      padding: 10px 20px;
      border-radius: 10px;
      color: #a2a9af;
      transition: all 0.3s ease-in-out;
      width: 120px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .options a {
      color: #a2a9af;
      text-decoration: none;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: color 0.3s ease;
    }

    .options a::before {
      content: "";
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background-color: #ebedec;
      transform-origin: bottom right;
      transform: scaleX(0);
      transition: transform 0.3s ease;
    }

    .options a:hover::before {
      transform-origin: bottom left;
      transform: scaleX(1);
    }

    .options a:hover {
      color: #ebedec;
    }

    .weapp:hover iframe {
      display: block;
    }

    .weapp iframe {
      display: none;
      border: none;
      position: absolute;
      top: -80px;
      right: 180px;
    }

    @media screen and (max-width: 510px) {
      .weapp iframe {
        position: fixed;
        top: 40%;
        left: 50%;
        transform: translate(-50%, 0);
      }
      .options{
        flex-direction: row;
        justify-content: center;
        flex-wrap: wrap;
      }
      .options .options_list {
        width: 100px;
      }
      h1{
        margin: 0;
      }
      p.call {
        font-size: 1.5em;
      }
    }

    footer {
      display: flex;
      justify-content: center;
      color: #4c5870;
    }

    footer a {
      color: inherit;
      text-decoration: none;
      margin: auto 10px;
    }
  </style>
</body>

</html>
