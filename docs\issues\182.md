---
title: 定时任务配置格式
---

# 定时任务配置格式

支持采用 crontab 的格式配置定时任务，已经支持下面的任务类型:

- stop 关机
- play 播放歌曲
- play_music_list 播放列表
- tts 文字转语音
- refresh_music_list 刷新播放列表
- set_volume 设置音量
- set_play_type 设置播放类型，单曲循环 0 , 全部循环 1 , 随机播放 2 , 单曲播放 3 , 顺序播放 4
- set_pull_ask 设置是否拉取对话记录，每天定时关闭，可缓解风控问题
- reinit 重新初始化，每天执行一次可缓解登录失效问题

### 示例

```json
[
    {
        "expression": "0 8 * * 0-4",
        "name": "play",
        "did": "123456789",
        "arg1": "周杰伦晴天"
    },
    {
        "expression": "10 8 * * 0-4",
        "name": "stop",
        "did": "123456789"
    },
    {
        "expression": "0 9 * * *",
        "name": "play",
        "did": "123456789",
        "arg1": "周杰伦晴天"
    },
    {
        "expression": "0 10 * * *",
        "name": "play_music_list",
        "did": "123456789",
        "arg1": "周杰伦"
    },
    {
        "expression": "30 10 * * *",
        "name": "play_music_list",
        "did": "123456789",
        "arg1": "周杰伦|晴天"
    },
    {
        "expression": "0 7 * * *",
        "name": "tts",
        "did": "123456789",
        "arg1": "早上好！该起床了！"
    },
    {
        "expression": "0 3 * * *",
        "name": "refresh_music_list"
    },
    {
        "expression": "* * * * *",
        "name": "set_volume",
        "did": "123456789",
        "arg1": "25"
    },
    {
        "expression": "* * * * *",
        "name": "set_play_type",
        "did": "123456789",
        "arg1": "2"
    },
   {
        "expression": "0 6 * * *",
        "name": "set_pull_ask",
        "arg1": "enable"
   },
   {
        "expression": "0 0 * * *",
        "name": "set_pull_ask",
        "arg1": "disable"
   },
   {
        "expression": "0 1 * * *",
        "name": "reinit"
   }
]
```

示例中的意思是：

- 周一到周五每天 8 点播放歌曲 "周杰伦晴天"
- 周一到周五每天 8 点 10 分执行关机指令
- 每天 9 点播放歌曲 "周杰伦晴天"
- 每天 10 点播放列表 "周杰伦"
- 每天 10 点 30 分播放列表 "周杰伦" 里的 "晴天"
- 每天 7 点发出语音 "早上好！该起床了！"
- 每天 3 点刷新播放列表，用于自动更新目录下的歌曲到播放列表里。
- 每分钟设置音量为 25
- 每分钟设置为随机播放
- 每天早上6点开启拉取对话记录
- 每天晚上12点关闭拉取对话记录
- 每天1点重新初始化

> 注意星期一是0，星期二是1，星期日是6。
> (0-6 or mon,tue,wed,thu,fri,sat,sun)
> The first weekday is always monday.

### 参数意思

- expression 的格式是标准的 crontab 的格式，用于配置任务的执行时机，如何配置可以直接用下面的 crontab ai 工具生成
  - <https://cronly.app/ai>
  - <https://cronify.zimo.li/>
- name 是任务名，目前只支持上面那几种。
- did 是小爱音箱的设备ID，就是设置页面的音箱型号后面的那串数字。
- arg1 根据任务不同而不同。
  - play 的 arg1 表示要播放的歌曲名。
  - play_music_list 的 arg1 表示要播放的播放目录名，可以加 `|` 符合加上目录下面的歌曲名，也可不加。
  - tts 的 arg1 表示要说的语音文字。

可以去 <https://www.json.cn/> 里检查 json 配置是否合法。 

## 评论


### 评论 1 - hanxi

0.3.38版本功能。

---

### 评论 2 - F-loat

<img src="https://gproxy.hanxi.cc/proxy/user-attachments/assets/9e562aea-423e-479d-bf0c-9932b330cf98" width="25%" />

小程序已支持可视化配置定时任务

---

### 评论 3 - hanxi

有想法增加一个参数来决定法定工作日，法定节假日执行。

---

### 评论 4 - hanxi

计划这样配工作日或者休息日，末尾加上 ` #workday` 表示工作日才执行，末尾加上 ` #offday` 表示休息日才执行。
```json
[
  {
    "expression": "0 8 * * * #workday",
    "name": "play",
    "did": "123456789",
    "arg1": "周杰伦晴天"
  },
  {
    "expression": "0 10 * * * #offday",
    "name": "play",
    "did": "123456789",
    "arg1": "周杰伦晴天"
  }
]
```

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/182)
