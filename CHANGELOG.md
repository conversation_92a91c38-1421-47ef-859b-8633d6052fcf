## v0.3.83 (2025-06-12)

### Feat

- 新增开关控制是否开始谷歌统计 see #473
- 支持b站合集和收藏下载 (#487)

### Fix

- 修复安全问题
- 修复安全问题

## v0.3.82 (2025-05-30)

### Fix

- 修复节假日文件没有打包到 docker 镜像里的问题

## v0.3.81 (2025-05-28)

### Feat

- 定时任务支持工作日和休息日 see #182

### Fix

- 动态小程序码生成接口 (#478)
- 指定日志编码,避免中文乱码 (#475)

## v0.3.80 (2025-05-18)

### Feat

- 新增 OH2 型号的支持
- 支持配置最大搜索歌曲数量 see #462

### Fix

- 修复获取在线歌曲长度 (#469)

## v0.3.79 (2025-04-29)

### Fix

- 修复型号LX05不能播放问题

## v0.3.78 (2025-04-09)

### Feat

- 优化获取时长的方式

### Fix

- 修复部分型号单曲播放功能无效的问题
- 监控文件夹仅对音乐格式生效，减少不必要的刷新 (#441)
- 修复型号X4B不能播放问题

## v0.3.77 (2025-03-13)

### Feat

- OH2P 型号支持打断说话

### Fix

- 修复首页看不到设备的问题

## v0.3.76 (2025-03-11)

### Feat

- 整理第三方播放设备的代码
- 删除歌曲口令默认关闭
- 新增删除歌曲口令 see #402
- 增加音量控制for -3thplay.html by socketio.emit (#404)
- 增加大声,音量,继续的指令,用于3thplay (#401)
- 不用再手动配置 tts command, 优先使用已知的 tts command
- 新增重新初始的定时任务 #182
- 新增定时任务用于开启和关闭拉取对话记录 #182
- 加入了遥控网页播放，用于实现电视上使用 (#395)

### Fix

- tailwind播放页面有报错，暂时禁用
- tailwind 主题问题
- 修复下载后不自动播放的问题
- 修复每次打开页面都是随机播放的问题

## v0.3.75 (2025-02-18)

### Feat

- 监测音乐文件夹变化更新歌曲列表 (#394)
- 添加正在播放页面 (#386)
- 更新tailwind主题  (#383)

### Fix

- 未开启模糊匹配播放错误问题 (#393)
- 修复tailwind主题样式问题 (#384)

## v0.3.74 (2025-01-21)

### Feat

- 新增 Tailwind 主题
- 修改设置页面文档链接

### Fix

- 修复下载歌单重命名问题

## v0.3.73 (2025-01-16)

### Fix

- 当前歌曲不在列表中时才切换列表 close #359
- 修复默认主题播放进度时间问题
- 尝试修复获取对话记录失败的问题 close #362

## v0.3.72 (2025-01-11)

### Feat

- 新增播放文字功能

### Fix

- 修复默认主题没有单曲循环的问题 see #355

## v0.3.71 (2025-01-07)

### Feat

- 支持自动填 ip 和端口

### Fix

- 搜索歌曲窗口不自动关闭 see #351
- 解决歌词信息写入失败的问题
- 修复一些小问题
- 非播放中也返回歌曲时长 see #340

## v0.3.70 (2025-01-04)

### Fix

- 尝试修复部分设备无法启动的问题
- 解决首页提示翻译英文问题
- 尝试解决 supervisor 启动报错

## v0.3.69 (2025-01-01)

### Feat

- 支持关闭获取对话记录功能

### Fix

- 尝试解决网络卡顿问题

## v0.3.68 (2024-12-31)

### Feat

- umami 脚本改为异步加载
- 支持 python3.13 版本
- 增加均衡歌曲响度（可选） (#338)

### Fix

- 修复保存设置时可能出现报错的情况

## v0.3.67 (2024-12-29)

### Feat

- 简化设置，不允许修改监听端口

### Fix

- 修复默认主题搜索问题

## v0.3.66 (2024-12-26)

### Fix

- 修复歌曲批量重命名的问题
- 修复自定义歌单删除后没刷新歌单列表
- 尝试修复更新失败问题

## v0.3.65 (2024-12-24)

### Fix

- 处理图像报错
- 修改歌单名字漏更新歌单列表
- 修复获取自定义歌单接口报错

## v0.3.64 (2024-12-22)

### Fix

- 使用自己架设的 sentry 服务，解决 Cloudflare 额度超量问题

## v0.3.63 (2024-12-22)

### Perf

- 只监控报错信息

## v0.3.62 (2024-12-21)

### Fix

- 修复首次配置时，默认主题只有一个设备的问题。
- 修复一些报错问题

## v0.3.61 (2024-12-19)

### Fix

- 尝试修复更新问题

### Refactor

- 重构更新流程

## v0.3.60 (2024-12-19)

## v0.3.59 (2024-12-19)

### Feat

- 新增更多的歌单编辑相关接口
- 一键更新功能
- 接入 sentry 平台
- 实现更新接口
- 下载完成之后修改文件权限为755 close #316

### Fix

- 解决飞牛平台报错问题

### Refactor

- 优化代码日志级别
- 更新等消息推送到客户端再重启
- 更新接口修改

## v0.3.58 (2024-12-15)

### Fix

- 尝试解决七牛设备问题
- 修复 umami 统计函数报错，解决七牛环境问题

## v0.3.57 (2024-12-14)

### Feat

- 优化批量下载工具命名和下载高码率音频
- 新增搜索播放口令用于生成临时播放列表
- 新增设置项 ignore_tag_dirs 用于忽略读取目录下的标签信息，解决挂载 alist 目录的问题
- 新主题 Material (#299)

### Fix

- 默认主题刷新时选中当前播放歌曲
- 修复当前播放列表没更新的问题
- 修复搜索时不显示保存输入框问题
- 收藏 (#301)
- 修复收藏歌曲失败的问题
- 小屏幕设备主页显示问题 (#300)

### Refactor

- 修改默认主题
- 后端也加入 umami 统计
- 新增自托管 umami 统计
- XIAOMUSIC_HOSTNAME 携带端口号友好提醒并处理 (#303)
- 修改标题
- 冲突解决错误

## v0.3.56 (2024-12-07)

### Feat

- tag 信息支持写入到歌曲文件 see #266
- 开启gzip压缩

### Fix

- 播放失败设置重试次数10次，解决设备失联后无限重试的问题
- 修复最近新增歌单问题
- 小程序码移动端布局兼容 (#293)

## v0.3.55 (2024-12-04)

### Fix

- 修复播放接口报错问题

## v0.3.54 (2024-12-04)

### Feat

- 新增最近新增歌单 close #273

### Fix

- 安卓低版本webview对audio的src为空值的报错 (#289)
- 修复M01语音播放问题，X08C X08E X8F 型号默认采用型号兼容模式 see #30

## v0.3.53 (2024-12-03)

### Fix

- 解决播放接口修改后播放失败的问题

## v0.3.52 (2024-12-03)

### Fix

- 修复播放接口参数错误的问题

## v0.3.51 (2024-12-03)

### Fix

- 修复空配置启动失败问题 close #284

## v0.3.50 (2024-12-03)

### Feat

- 修改日志文件的默认值
- 新增修改tag缓存信息的接口 close #266
- 新增专用的播放歌曲和播放歌单接口，解决默认口令提示词被修改了导致后台失效的问题
- 统计设备型号
- 页面与设置中的HOST不一致时弹窗提醒 (#281)
- 未发现小爱设备时给予提示 (#278)
- 优化设置页面提示

### Fix

- 更新 yt-dlp ，解决 B 站下载问题 close #279
- 网页播放audio获取到错误url无法播放时提醒用户 (#280)
- input标签自闭合

### Refactor

- 调整设置页面

## v0.3.49 (2024-11-28)

### Feat

- 临时文件目录支持配置 #99
- 新增单曲播放和顺序播放功能 close #277
- 设置播放类型支持配置语音提示词，定时任务支持设置播放类型

### Fix

- 修复中文数字转换函数对'十、十一'等数字的处理 (#275)

## v0.3.48 (2024-11-20)

### Feat

- 支持替换默认口令，而不是追加 close #259
- 新增自定义个歌单接口 #242

### Fix

- 锁定 PWA 应用旋转方向

## v0.3.47 (2024-11-14)

### Feat

- 支持 PWA 应用安装
- 新增模糊匹配测试用例

### Fix

- 修复 PWA 应用有密码时报错的问题
- 修复播放顺序没有按数字排序的问题 close #249

## v0.3.46 (2024-11-08)

### Feat

- 升级依赖库

### Fix

- 添加依赖库 requests

### Refactor

- 依赖库已经支持分段获取静态文件，重构代码

## v0.3.45 (2024-11-08)

### Feat

- 定时任务支持设置音量
- 播放歌单口令支持配置

### Fix

- 修复定时任务报错问题

## v0.3.44 (2024-11-01)

### Feat

- 日志时间里加上日期

### Fix

- 修复搜索失败的问题

## v0.3.43 (2024-10-30)

### Feat

- 播放列表可以删除当前歌曲(!危险操作，请在设置中心开启相关功能) (#250)
- 插件自定义口令支持获取语音输入内容 #105

### Fix

- 修复谷歌统计导致的卡顿问题
- 解决挂载网盘卡死的问题

## v0.3.42 (2024-10-24)

### Fix

- 尝试修复缺少 libtiff.so.6 文件的问题 #244
- 修复默认主题播放歌曲输入框空的情况
- 尝试修复停止后自动播放的问题

## v0.3.41 (2024-10-17)

### Feat

- 设置默认时区为东八区 closed #236

### Fix

- 修复获取标签信息报错问题
- remove_id3_tags return None if no id3 tag (#238)
- bug in del_music (#237)

## v0.3.40 (2024-10-16)

### Feat

- 默认主题的播放列表上显示歌曲数量

### Fix

- 修复播放卡顿问题（谷歌统计地址无法访问的情况）

## v0.3.39 (2024-10-15)

### Feat

- 固定的播放列表全部初始化
- 生产环境与开发环境接口分离、关于页面增加返回到主页的链接
update: 支持https页面未及时更新的问题

### Fix

- pure主题 当前设备与远程设备未正确区分的问题 (#234)
- static和doc添加basic auth (#231)

### Refactor

- 修改默认UI播放提示词 (#233)

## v0.3.38 (2024-10-14)

### Feat

- 播放状态接口返回当前播放列表 (#229)
- 新增口令收藏歌曲用来收藏当前播放的歌曲
- 默认UI搜索框动态显示 (#228)
- 文件转换逻辑延迟到读取文件的时候 see #218
- 重写播放组件，现在支持歌词显示了
- 使用 /cmdstatus 接口来判断异步任务是否完成
- 新增接口 /cmdstatus 用于查询异步任务是否执行完毕
- XMusicPlayer播放器主题优化 (#216)
- XMusicPlayer播放器主题 (#214)
- 新增 yt-dlp cookies 文件参数支持
- 新增批量下载歌曲工具
- 新增后台网站图标
- 加密音乐和图片访问链接 (#200)
- 歌曲信息中的图片改为url #190
- 新增更新提醒
- 定时任务新增刷新播放列表接口
- 后台设置名称优化
- 新增按钮刷新 tag 信息
- 新增 musicinfos 接口用于批量查询歌曲信息
- 增加 tags 缓存 (#193)
- 使用 opencc 将歌曲名转化为简体 (#192)
- 搜索的歌曲存成列表供前端显示,实现额外索引 (#188)
- 搜索多个结果，并更新“当前”播放列表 (#185)
- musicinfo接口新增musictag参数，用于返回歌曲额外信息
- 新增口令【播放列表第几个+列表名】来播放列表里的第几个 #158
- 新增定时任务功能 #182
- hostname can take protocol，域名支持 https 格式 (#181)

### Fix

- xplayer 收藏歌曲、取消收藏 (#230)
- 修复型号M01获取对话记录时间戳的问题
- 修复型号M01无法获取到对话记录的问题
- 使用小爱设备播放时组件异常的问题 (#217)
- 修复图片获取失败的问题
- 修复 yt-dlp-cookies 报错
- 修复自定义口令末尾多余逗号的情况
- 修复windows下路径问题
- 解决 L05C 无提示音问题 support MiIOService tts (#198)
- 解决歌曲信息乱码问题
- 修复搜索补全不生效的问题
- 修复默认主题没有选中上次播放列表的问题
- ffmpeg only output audio (#184)

### Refactor

- 新增清理缓存按钮
- 优化默认UI的搜索框#226 (#227)
- 修复告警
- 体验优化，音乐列表缓存 (#222)
- 修改为播放选中歌曲
- 更新静态文件

### Perf

- 对歌曲信息中的图片缩小到300 #190

## v0.3.37 (2024-09-20)

### Feat

- Pure主题更新 设置中心新增主题音乐列表样式选择、夜间模式、其他多项优化 (#180)

## v0.3.36 (2024-09-19)

### Feat

- Pure 主题更新 (#178)
- 支持配置获取对话记录间隔时间 #169
- 允许在后台设置监听端口

### Fix

- 修复开启继续播放时歌曲播放不完整问题 (#177)

## v0.3.35 (2024-09-18)

### Feat

- 允许跨域访问 #172

### Fix

- 修复 Pure 主题白屏无法打开的问题 (#176)

## v0.3.34 (2024-09-18)

### Feat

- 新增 pure 主题 vue + elementUI (#172)

### Fix

- 主页适配移动端
- 修复网页播放点击后没有关闭旧声音的问题 #166
- 修复单曲循环的情况下歌曲不在当前播放列表时失效的情况

### Refactor

- 优化代码:输入框处理抖动问题，网页播放修改实现方式 see #166

## v0.3.33 (2024-09-15)

### Feat

- 调整页面布局
- 支持继续播放 (#171)

### Fix

- #168 安全优化: 设置数据接口密码隐藏处理
- 修复谷歌统计报错问题

### Refactor

- 优化谷歌统计

## v0.3.32 (2024-09-14)

### Feat

- 新增谷歌统计
- 增加播放进度 (#160)

### Fix

- 优化audio_id查询方式 (#165)
- 播放链接接口支持复杂的链接

## v0.3.31 (2024-09-10)

### Feat

- 新增播放上一首歌曲功能 #90
- 新增所有歌曲列表
- 触屏版显示歌曲名称 (#156)

### Fix

- 修复插件示例报错 #105
- 修复当前播放歌曲没保存的问题 #90

## v0.3.30 (2024-09-07)

### Feat

- 修改设置按钮位置
- 新增网页播放接口 #138

## v0.3.29 (2024-09-06)

### Feat

- 设置页面新增接口文档入口

### Fix

- 修复网页开启秘密验证无法播歌的问题 #149

## v0.3.28 (2024-09-03)

### Feat

- 新增歌曲收藏功能 #87

### Fix

- docker下minetypes无法判断m4a

### Refactor

- ffmpeg_location 从配置里读取

## v0.3.27 (2024-09-02)

### Feat

- Add feature as requested in issue #143

### Fix

- 默认下载目录修改

### Refactor

- 处理 code review 问题'

## v0.3.26 (2024-08-17)

### Feat

- 删除网关模式

## v0.3.25 (2024-08-16)

### Feat

- 设置页面支持配置 use_music_api 选项

## v0.3.24 (2024-08-01)

### Fix

- #131 修复多设备切换时播放模式显示错误问题

## v0.3.23 (2024-08-01)

### Fix

- 修复部分文件获取不到播放时长问题
- 处理安全问题

## v0.3.22 (2024-08-01)

### Feat

- 网关模式支持配置，默认关闭

### Fix

- 继续优化延迟问题

## v0.3.21 (2024-07-30)

### Feat

- 尝试加个网关在前面处理静态文件来加速文件获取

### Fix

- 使用前置网关处理静态文件来加速，尝试解决延迟的问题
- 播放前先立即暂停之前的音乐

## v0.3.20 (2024-07-30)

### Fix

- 尝试修复延迟问题，修复播放停止不了的问题

## v0.3.19 (2024-07-30)

### Fix

- 调整配置，优化获取歌曲时长接口

## v0.3.18 (2024-07-29)

### Fix

- #135 修复获取不到播放时长时只播放3秒的问题

## v0.3.17 (2024-07-28)

### Fix

- 优化日志输出，尝试排查延迟播放的问题

## v0.3.16 (2024-07-28)

## v0.3.15 (2024-07-28)

### Fix

- 修复自定义口令重复的问题
- 修复日志输出问题
- 修复退出异常问题

## v0.3.14 (2024-07-28)

### Feat

- 优化播放延迟问题，并新增配置下一首播放的延迟秒数

## v0.3.13 (2024-07-24)

### Fix

- 解决 docker 镜像问题

## v0.3.12 (2024-07-24)

### Feat

- 优化获取文件播放时长接口，尝试解决播放延迟和操作面板卡顿的问题

## v0.3.11 (2024-07-22)

### Feat

- Add remove mp3 id3 tag function

### Fix

- #130 单曲循环的模式下，播放列表的指令不生效

### Refactor

- 优化代码

## v0.3.10 (2024-07-19)

### Feat

- 支持软连接的接口直接用os.walk即可

### Fix

- 修复软连接目录不能播放的问题
- 修复自定义语音口令设置不生效的问题

## v0.3.9 (2024-07-17)

### Feat

- #119 音乐目录支持软连接

### Fix

- 修复日志下载报错问题
- 兼容旧的setting.json文件中conf_path为空的情况
- 修复设置页面可能打不开的问题

## v0.3.8 (2024-07-16)

### Fix

- 修复播放url接口问题

## v0.3.7 (2024-07-16)

### Feat

- 播放链接按钮对应给个默认的链接用于测试
- Uvicorn 的日志信息合并到 xiaomusic 日志里显示

## v0.3.6 (2024-07-15)

### Fix

- #126 修复pip安装时主页打不开的问题

## v0.3.5 (2024-07-15)

### Fix

- #116 播放失败自动切下首歌

## v0.3.4 (2024-07-15)

### Fix

- #125 修复本地英文歌曲匹大小写字母配不到的问题

## v0.3.3 (2024-07-15)

### Fix

- 尝试修复播放卡顿问题 see #124

## v0.3.2 (2024-07-15)

### Fix

- #122 pip安装方式下，static目录找不到报错
- 版本更新时更新页面缓存

## v0.3.1 (2024-07-15)

### Fix

- 修复主页选择设备不生效的问题 see #120

## v0.3.0 (2024-07-14)

### Feat

- 建议音乐目录和配置目录分开不同目录
- 优化后台网络设置，同时支持ipv4和ipv6
- 使用fastapi替换flask,解决多线程问题
- #106 网页上显示音箱当前状态（播放中or空闲中）以及当前的播放模式
- 优化首页加载慢的问题
- 优化设置页面布局，方便配置必须项
- 优化配置界面，支持配置分组
- 支持多设备分开播放 see #65

### Fix

- #114 修复部分 mp3 文件长度识别错误
- 删除 armv6 的支持
- 修复编译问题
- 修复音乐路径设置后找不到音乐的问题
- 修复启动报错的问题
- 修复CI警告问题

## v0.2.0 (2024-07-09)

### Feat

- 触屏版可以不用设置 XIAOMUSIC_USE_MUSIC_API
- 升级依赖库
- 唤醒口令配置支持配语音词，简化自定义口令配置 see #105

## v0.1.101 (2024-07-07)

### Fix

- #81 修复播放列表时，当前歌曲不在列表没有更换歌曲的问题
- #110 修复配置加载问题

## v0.1.100 (2024-07-07)

### Fix

- 日志代码写错

## v0.1.99 (2024-07-07)

### Fix

- #81 修复播放列表没有继续播放上次播放的歌曲，并把随机播放，全部循环，单曲循环状态落地

## v0.1.98 (2024-07-07)

### Fix

- 修复多设备获取不到对话记录的问题 see #65
- #93 修复目录深度设置后导致目录下的歌曲无法加到播放列表里的问题

## v0.1.97 (2024-07-06)

### Fix

- 修复网页控制台设置页面保存报错

## v0.1.96 (2024-07-06)

### Feat

- 使用commitizen管理版本号
- 页面版本号链接到CHANGELOG页面
- 规范版本管理

## v0.1.95 (2024-07-06)

## v0.1.94 (2024-07-06)

### Feat

- 优化多设备接口执行效果，尽量做到同时执行

### Fix

- 新增参数配置强制打断小爱说话
- 修复多设备获取对话记录的问题
- 修复windows下路径分隔符被视为转移符导致音箱无法播放音乐的问题
- 修复播放链接报错
- 修复配置页面默认配置被置空的问题

## v0.1.93 (2024-07-05)

### Feat

- 访问账号密码默认为空
- 支持下载的目录与本地音乐目录分开 see #98
- 新增m4a文件格式支持
- 设置页面支持配置多设备
- 默认用空的后台账号和密码
- 支持多个设备同时播放 see #65
- 新增自定义口令功能 #105

### Fix

- 修复设置页面没成功初始化设置问题
- 修复镜像缺少文件问题
- 尝试解决插件路径问题
- 设置页面日志路径写错了
- 修复口令导致异常关闭的问题

## v0.1.92 (2024-07-04)

### Feat

- 启动参数新增 --port 配置监听端口
- 外网访问端口可独立配置
- 优化设置页面，新增更多配置项
- 首次保存设置后不需要重启容器

### Fix

- 日志文件配置的环境变量写错了

## v0.1.91 (2024-07-03)

### Fix

- 尝试解决触屏版不能播放的问题

## v0.1.90 (2024-07-02)

### Feat

- 优化触屏版播放页面显示歌曲

## v0.1.89 (2024-07-02)

### Feat

- 尝试解决触屏版无法播放的问题

### Fix

- 播放歌曲写成固定的了
- 播放歌曲时被其他指令打断后没有继续播放

## v0.1.88 (2024-07-02)

### Feat

- 日志里不要输出敏感信息
- 优化下载 ffmpeg 脚本，尝试解决 armv7 环境问题
- 优化日志输出信息
- 尝试解决触屏版无法播放的问题

### Fix

- 是否下载中判断错误导致播放无法自动重新开始播放
- 升级yt-dlp到2024.07.01
- 修复部分型号关机失败的问题

## v0.1.87 (2024-07-01)

### Fix

- 修复XIAOMUSIC_USE_MUSIC_API=true时播放不了的问题

## v0.1.86 (2024-07-01)

### Feat

- 优化 ffmpeg 安装脚本
- 新增调试工具用来调试 player_play_music 接口
- 升级依赖库 MiService

### Fix

- 尝试修复 armv7 的 ffmpeg 问题
- 尝试修复关机失败的问题
- 修复口令不能播放的问题

## v0.1.85 (2024-06-30)

### Feat

- 版本号链接到github的release页面，方便查看版本更新日志

### Fix

- 修复电台删除后没有从电台列表中删除的问题

## v0.1.84 (2024-06-30)

### Feat

- config.json 支持更多配置选项
- 新增 XIAOMUSIC_STOP_TTS_MSG 配置关机提示音

## v0.1.83 (2024-06-30)

## v0.1.82 (2024-06-30)

### Feat

- 优化指令匹配规则

## v0.1.81 (2024-06-30)

## v0.1.80 (2024-06-30)

### Fix

- #91 修复下载歌曲报错

## v0.1.79 (2024-06-29)

## v0.1.77 (2024-06-29)

### Fix

- #52 支持配置模糊匹配本地歌曲

## v0.1.76 (2024-06-28)

## v0.1.75 (2024-06-28)

## v0.1.74 (2024-06-28)

## v0.1.73 (2024-06-28)

## v0.1.72 (2024-06-28)

## v0.1.71 (2024-06-28)

### Fix

- #83

## v0.1.70 (2024-06-27)

## v0.1.69 (2024-06-26)

## v0.1.67 (2024-06-26)

## v0.1.66 (2024-06-26)

## v0.1.65 (2024-06-26)

## v0.1.64 (2024-06-26)

## v0.1.62 (2024-06-25)

## v0.1.61 (2024-06-25)

## v0.1.60 (2024-06-25)

## v0.1.58 (2024-06-25)

### Fix

- 登陆失败不阻塞启动

## v0.1.57 (2024-06-24)

## v0.1.56 (2024-06-24)

## v0.1.55 (2024-06-23)

### Fix

- #47 支持配置基础的BaseAuth登录

## v0.1.54 (2024-06-23)

### Fix

- #76 新增XIAOMUSIC_MUSIC_PATH_DEPTH配置生成播放列表的目录深度，默认10
- #74 配置目录可以和下载目录分开配置, 新增XIAOMUSIC_CONF_PATH用来设置配置目录，不配置时使用下载目录

## v0.1.53 (2024-06-23)

## v0.1.52 (2024-06-21)

## v0.1.51 (2024-06-20)

## v0.1.49 (2024-06-20)

## v0.1.48 (2024-06-16)

## v0.1.47 (2024-06-16)

## v0.1.46 (2024-06-15)

## v0.1.45 (2024-06-15)

## v0.1.44 (2024-06-14)

## v0.1.43 (2024-06-14)

## v0.1.41 (2024-06-14)

## v0.1.40 (2024-06-12)

## v0.1.39 (2024-06-12)

## v0.1.38 (2024-06-12)

### Fix

- #70 下一首歌曲不存在时从播放列表中删除并继续找下一首

## v0.1.37 (2024-06-04)

## v0.1.36 (2024-05-30)

## v0.1.35 (2024-05-30)

### Fix

- #67 没配置did时也允许启动 http 服务

## v0.1.34 (2024-05-19)

## v0.1.33 (2024-05-19)

### Fix

- #50 新增配置页面
- #62

## v0.1.32 (2024-05-17)

## v0.1.31 (2024-05-16)

## v0.1.30 (2024-05-16)

### Fix

- 控制台显示版本号 #59

## v0.1.29 (2024-05-16)

### Fix

- #57 #55

## v0.1.28 (2024-05-16)

## v0.1.27 (2024-05-16)

## v0.1.26 (2024-05-08)

## v0.1.25 (2024-05-06)

## v0.1.24 (2024-04-30)

## v0.1.23 (2024-04-30)

## v0.1.22 (2024-04-30)

## v0.1.21 (2024-04-08)

## v0.1.20 (2024-04-08)

## v0.1.19 (2024-04-04)

## v0.1.18 (2024-02-24)

## v0.1.16 (2024-02-24)

## v0.1.15 (2024-02-03)

## v0.1.14 (2024-02-03)

## v0.1.13 (2024-02-02)

## v0.1.12 (2024-01-30)

### Fix

- set volume failed

## v0.1.11 (2024-01-29)

## v0.1.10 (2024-01-29)

## v0.1.9 (2024-01-28)

### Fix

- arg1 漏修改

## v0.1.8 (2024-01-28)

### Fix

- http server listen host

## v0.1.7 (2024-01-28)

## v0.1.6 (2024-01-28)

## v0.1.5 (2024-01-27)

## v0.1.4 (2024-01-27)

### Fix

- error when play next

## v0.1.3 (2023-10-15)

## v0.1.2 (2023-10-15)

## v0.1.1 (2023-10-14)
