---
title: 已知 ttsCommand
---

# 已知 ttsCommand

参考： https://github.com/idootop/mi-gpt/blob/main/docs/compatibility.md

- 小爱音箱 Pro	LX06	`5-1`
- 小爱音箱 mini	LX01	`5-1`
- 小爱音箱 Play（2019 款）	LX05	`5-1`
- 小爱音箱 万能遥控版	LX5A	`5-1`
- 小米 AI 音箱	S12	     `5-1`
- 小米 AI 音箱（第二代）	L15A	`7-3`
- 小爱智能家庭屏 10	X10A	`7-3`
- <PERSON><PERSON> Sound Pro	L17A	`7-3`
- 小爱音箱	L06A	`5-1`
- 小爱音箱 Play	L05B	`5-3`
- 小米小爱音箱 Play 增强版	L05C	`5-3`
- <PERSON><PERSON> 智能家庭屏 6	X6A	`7-3`
- <PERSON><PERSON> 小爱触屏音箱 Pro 8 英寸	X08E	`7-3`
- 小爱音箱 Art	L09A	`3-1`
- 小爱触屏音箱	LX04	`5-1`

如果你是其他型号的小爱音箱，且不能语音转文字播放，欢迎分享你的型号的 ttsCommand 。

## 评论


### 评论 1 - hanxi

可以不用手动配置了，写到代码里了。

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/365)
