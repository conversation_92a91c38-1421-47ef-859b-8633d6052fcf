name: Build Docker Base Image

on:
  push:
    branches: ["main"]
    paths:
      - 'Dockerfile.builder'
      - 'Dockerfile.runtime'
      - 'install_dependencies.sh'
      - '.github/workflows/build-base-image.yml'
  workflow_dispatch:

permissions:
  contents: read

jobs:
  build-image:
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request'
    steps:
      - uses: actions/checkout@v4
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Build and push runtime
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./Dockerfile.runtime
          platforms: linux/amd64, linux/arm64, linux/arm/v7
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/xiaomusic:runtime

      - name: Build and push builder
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./Dockerfile.builder
          platforms: linux/amd64, linux/arm64, linux/arm/v7
          push: true
          tags: ${{ secrets.DOCKERHUB_USERNAME }}/xiaomusic:builder

