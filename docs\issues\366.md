---
title: 多设备单独播放功能设计
---

# 多设备单独播放功能设计

## 分组功能

为设备设计分组功能，可以把一个或者多个设备加入到一个分组，一个分组内的设备会被控制同时播放。设备的音量需要支持独立配置。

分组用 group_list 字段配置，比如 `did1:客厅,did2:客厅` 表示 did1和did2用同一个组名。不配置这个参数就说明一个设备一个分组。后台勾选设备的意图改为哪些设备可以接入。比如: 

`319762914:a,319518426:a,1236547:b`

单个设备的存储配置结构如下:

```json
{
  "devices": {
    "10086": {
      "cur_music": "当前播放的歌曲",
      "cur_playlist": "当前播放的列表",
      "name": "客厅的小爱1",
      "play_type": 1
    },
    "10087": {
      "cur_music": "当前播放的歌曲",
      "cur_playlist": "当前播放的列表",
      "name": "客厅的小爱2",
      "play_type": 1
    }
  }
}
```

设备名字从音箱app设置里读取过来，不用在后台修改。

配置采用组名相同为一组，可以考虑不设置组名就用设备名作为组名，也就是一个设备为一组。

后端提供的接口都改为支持操作多个设备，

单曲循环，随机播放这些操作都会同步修改同一个组里的其他设备。

歌单列表所有设备共享，当前选择的歌单和当前播放的歌曲组内共享，一个组共用一份播放列表，也就是共用一个定时器。

## 主页设备切换

主页主要是用于播放操作的，所有操作都是针对于单个设备的，所以在顶部加入一个切换设备的功能。除了修改声音不会同步修改组内设备，其他操作都会同时操作组内所有设备。

> /issues/65.html#issuecomment-2215736529

## 评论

没有评论。
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/366)
