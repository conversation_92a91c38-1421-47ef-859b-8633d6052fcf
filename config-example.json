{"account": "", "password": "", "mi_did": "", "cookie": "", "verbose": false, "music_path": "music", "download_path": "", "conf_path": null, "tag_cache_dir": null, "hostname": "***********", "port": 8090, "public_port": 0, "proxy": null, "search_prefix": "bilisearch:", "ffmpeg_location": "./ffmpeg/bin", "active_cmd": "play,set_random_play,playlocal,play_music_list,stop,音量,大点音,小点音,继续", "exclude_dirs": "@eaDir", "music_path_depth": 10, "disable_httpauth": true, "httpauth_username": "", "httpauth_password": "", "music_list_url": "", "music_list_json": "", "disable_download": false, "key_word_dict": {"播放歌曲": "play", "播放本地歌曲": "playlocal", "关机": "stop", "下一首": "play_next", "单曲循环": "set_play_type_one", "全部循环": "set_play_type_all", "随机播放": "set_random_play", "分钟后关机": "stop_after_minute", "播放列表": "play_music_list", "刷新列表": "gen_music_list", "本地播放歌曲": "playlocal", "放歌曲": "play", "暂停": "stop", "停止": "stop", "停止播放": "stop", "音量": "set_myvolume", "继续": "stop", "大点音": "exec#setmyvolume(\"up\")", "小点音": "exec#setmyvolume(\"down\")", "测试自定义口令": "exec#code1(\"hello\")", "测试链接": "exec#httpget(\"https://github.com/hanxi/xiaomusic\")"}, "key_match_order": ["分钟后关机", "播放歌曲", "下一首", "单曲循环", "全部循环", "随机播放", "关机", "刷新列表", "播放列表", "播放本地歌曲", "本地播放歌曲", "放歌曲", "暂停", "停止", "音量", "继续", "大点音", "小点音", "停止播放", "测试自定义口令", "测试链接"], "use_music_api": false, "use_music_audio_id": "1582971365183456177", "use_music_id": "355454500", "log_file": "/tmp/xiaomusic.txt", "fuzzy_match_cutoff": 0.6, "enable_fuzzy_match": true, "stop_tts_msg": "收到,再见", "enable_config_example": true, "keywords_playlocal": "播放本地歌曲,本地播放歌曲", "keywords_play": "播放歌曲,放歌曲", "keywords_stop": "关机,暂停,停止,停止播放", "user_key_word_dict": {"测试自定义口令": "exec#code1(\"hello\")", "测试链接": "exec#httpget(\"https://github.com/hanxi/xiaomusic\")"}, "enable_force_stop": false, "devices": {}, "group_list": "", "convert_to_mp3": false}