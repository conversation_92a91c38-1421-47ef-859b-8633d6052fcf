<!DOCTYPE html>
<html>
  <head>
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width">
    <title>小爱音箱操控面板</title>
    <script src="./jquery-3.7.1.min.js?version=1733563859"></script>
    <script src="./setting.js?version=1733563859"></script>
    <link rel="stylesheet" type="text/css" href="./style.css?version=1733563859">

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-Z09NC1K7ZW"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments)};
    gtag('js', new Date());
    gtag('config', 'G-Z09NC1K7ZW');
    </script>

    <!-- umami -->
    <script async defer src="https://umami.hanxi.cc/script.js" data-website-id="7bfb0890-4115-4260-8892-b391513e7e99"></script>

    <!--
<script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
<script>
var vConsole = new window.VConsole();
</script>
-->

  </head>
  <body>
    <h2>小爱音箱设置面板
      (<a id="version" href="https://xdocs.hanxi.cc/issues/changelog.html">版本未知</a>)
    </h2>
    <hr>

    <div class="rows">
      <label for="mi_did">*勾选设备(至少勾选1个):</label>
      <div id="mi_did">
      </div>
    </div>
    <br>

    <div id="setting">
      <div class="rows">
        <label for="account">*小米账号:</label>
        <input id="account" type="text" placeholder="填写小米登录账号" />

        <label for="password">*小米密码:</label>
        <input id="password" type="password" placeholder="填写小米登录密码" />

        <label for="hostname">*XIAOMUSIC_HOSTNAME(IP或域名):</label>
        <input id="hostname" type="text" />
      </div>
      <hr>
      <div class="rows">
        <label for="verbose">是否开启调试日志:</label>
        <select id="verbose">
          <option value="true" selected>true</option>
          <option value="false">false</option>
        </select>

        <label for="port">监听端口(修改后需要重启):</label>
        <input id="port" type="number" value="8090" />

        <label for="public_port">外网访问端口(0表示跟监听端口一致):</label>
        <input id="public_port" type="number" value="0" />

        <label for="group_list">设备分组配置:<a href="https://github.com/hanxi/xiaomusic/issues/65#issuecomment-**********" target="_blank">文档</a></label>
        <input id="group_list" type="text" placeholder="did1:组名1,did2:组名1,did3:组名2" />

        <label for="music_path">音乐目录:</label>
        <input id="music_path" type="text" value="music" />

        <label for="enable_file_watch">启用目录监控(自动刷新音乐列表):</label>
        <select id="enable_file_watch">
          <option value="true">开启</option>
          <option value="false" selected>关闭</option>
        </select>

        <label for="file_watch_debounce">刷新列表延迟时间(秒):</label>
        <input id="file_watch_debounce" type="number" value="10" />

        <label for="download_path">音乐下载目录(必须是music的子目录):</label>
        <input id="download_path" type="text" value='music/download' />

        <label for="conf_path">配置文件目录:</label>
        <input id="conf_path" type="text" />

        <label for="cache_dir">缓存文件目录:</label>
        <input id="cache_dir" type="text" />

        <label for="temp_path">临时文件目录:</label>
        <input id="temp_path" type="text" value="music/tmp" />

        <label for="ffmpeg_location">ffmpeg路径:</label>
        <input id="ffmpeg_location" type="text" value="./ffmpeg/bin" />

        <label for="log_file">日志路径:</label>
        <input id="log_file" type="text" value="xiaomusic.log.txt" />

        <label for="active_cmd">允许唤醒的命令:</label>
        <input id="active_cmd" type="text" value="play,random_play,playlocal,play_music_list,stop" />

        <label for="exclude_dirs">忽略目录(逗号分割):</label>
        <input id="exclude_dirs" type="text" value="@eaDir,tmp" />

        <label for="ignore_tag_dirs">不扫描标签信息目录(逗号分割):</label>
        <input id="ignore_tag_dirs" type="text" value="" />

        <label for="music_path_depth">目录深度:</label>
        <input id="music_path_depth" type="number" value="10" />

        <label for="search_prefix">XIAOMUSIC_SEARCH(歌曲下载方式):</label>
        <select id="search_prefix">
          <option value="bilisearch:">bilisearch:</option>
          <option value="ytsearch:">ytsearch:</option>
        </select>

        <label for="proxy">XIAOMUSIC_PROXY(ytsearch需要):</label>
        <input id="proxy" type="text" placeholder="http://192.168.2.5:8080" />

        <label for="loudnorm">均衡歌曲音量大小(loudnorm滤镜):</label>
        <input id="loudnorm" type="text" placeholder="loudnorm=I=-14:TP=-1.5:LRA=6" />

        <label for="remove_id3tag">去除MP3 ID3v2和填充:</label>
        <select id="remove_id3tag">
          <option value="true">true</option>
          <option value="false" selected>false</option>
        </select>

        <label for="convert_to_mp3">转换为MP3:</label>
        <select id="convert_to_mp3">
          <option value="true">true</option>
          <option value="false" selected>false</option>
        </select>

        <label for="disable_httpauth">关闭控制台密码验证:</label>
        <select id="disable_httpauth">
          <option value="true" selected>true</option>
          <option value="false">false</option>
        </select>
        <label for="httpauth_username">控制台账户:</label>
        <input id="httpauth_username" type="text" value="" />
        <label for="httpauth_password">控制台密码:</label>
        <input id="httpauth_password" type="password" value="" />

        <label for="disable_download">关闭下载功能:</label>
        <select id="disable_download">
          <option value="true">true</option>
          <option value="false" selected>false</option>
        </select>

        <label for="use_music_audio_id">触屏版显示歌曲ID:</label>
        <input id="use_music_audio_id" type="text" value="1582971365183456177" />
        <label for="use_music_id">触屏版显示歌曲分段ID:</label>
        <input id="use_music_id" type="text" value="355454500" />

        <label for="fuzzy_match_cutoff">模糊匹配阈值(0.1~0.9):</label>
        <input id="fuzzy_match_cutoff" type="number" value="0.6" />

        <label for="enable_fuzzy_match">开启模糊搜索:</label>
        <select id="enable_fuzzy_match">
          <option value="true" selected>true</option>
          <option value="false">false</option>
        </select>

        <label for="use_music_api">型号兼容模式:</label>
        <select id="use_music_api">
          <option value="true">true</option>
          <option value="false" selected>false</option>
        </select>

        <label for="continue_play">启用继续播放(可能导致兼容性问题):</label>
        <select id="continue_play">
          <option value="true">true</option>
          <option value="false" selected>false</option>
        </select>

        <label for="pull_ask_sec">获取对话记录间隔(秒):</label>
        <input id="pull_ask_sec" type="number" value="1" />

        <label for="delay_sec">下一首歌延迟播放秒数:</label>
        <input id="delay_sec" type="number" value="3" />

        <label for="stop_tts_msg">停止提示音:</label>
        <input id="stop_tts_msg" type="text" value="收到,再见" />
        <label for="play_type_one_tts_msg">单曲循环提示音:</label>
        <input id="play_type_one_tts_msg" type="text" value="已经设置为单曲循环" />
        <label for="play_type_all_tts_msg">全部循环提示音:</label>
        <input id="play_type_all_tts_msg" type="text" value="已经设置为全部循环" />
        <label for="play_type_rnd_tts_msg">随机播放提示音:</label>
        <input id="play_type_rnd_tts_msg" type="text" value="已经设置为随机播放" />
        <label for="play_type_sin_tts_msg">单曲播放提示音:</label>
        <input id="play_type_sin_tts_msg" type="text" value="已经设置为单曲播放" />
        <label for="play_type_seq_tts_msg">顺序播放提示音:</label>
        <input id="play_type_seq_tts_msg" type="text" value="已经设置为顺序播放" />

        <label for="keywords_playlocal">播放本地歌曲口令:</label>
        <input id="keywords_playlocal" type="text" value="播放本地歌曲,本地播放歌曲" />
        <label for="keywords_play">播放歌曲口令:</label>
        <input id="keywords_play" type="text" value="播放歌曲,放歌曲" />
        <label for="keywords_playlist">播放列表口令:</label>
        <input id="keywords_playlist" type="text" value="播放列表,播放歌单" />
        <label for="keywords_stop">停止口令:</label>
        <input id="keywords_stop" type="text" value="关机,暂停,停止,停止播放" />
        <label for="keywords_search_playlocal">本地搜索播放口令(会产生临时播放列表):</label>
        <input id="keywords_search_playlocal" type="text" value="本地搜索播放" />
        <label for="keywords_search_play">搜索播放口令(会产生临时播放列表):</label>
        <input id="keywords_search_play" type="text" value="搜索播放" />

        <label for="enable_yt_dlp_cookies">启用yt-dlp-cookies(需要先上传yt-dlp-cookies.txt文件):</label>
        <select id="enable_yt_dlp_cookies">
          <option value="true">true</option>
          <option value="false" selected>false</option>
        </select>

        <label for="enable_save_tag">启用ID3标签写入文件:</label>
        <select id="enable_save_tag">
          <option value="true">true</option>
          <option value="false" selected>false</option>
        </select>

        <label for="get_ask_by_mina">特殊型号获取对话记录:</label>
        <select id="get_ask_by_mina">
          <option value="true">true</option>
          <option value="false" selected>false</option>
        </select>

        <label for="recently_added_playlist_len">最近新增的歌曲数量:</label>
        <input id="recently_added_playlist_len" type="number" value="50" />

        <label for="music_list_url">歌单地址:</label>
        <input id="music_list_url" type="text" value="https://gist.githubusercontent.com/hanxi/dda82d964a28f8110f8fba81c3ff8314/raw/example.json" />

        <label for="music_list_json">歌单内容:<a href="https://github.com/hanxi/xiaomusic/issues/78" target="_blank">格式文档</a></label>
        <textarea id="music_list_json" type="text"></textarea>

        <label for="crontab_json">定时任务:<a href="https://github.com/hanxi/xiaomusic/issues/182" target="_blank">格式文档</a></label>
        <textarea id="crontab_json" type="text"></textarea>
      </div>
    </div>
    <hr>

    <div class="rows">
      <label for="yt_dlp_cookies_file">上传yt_dlp_cookies.txt文件:<a href="https://github.com/hanxi/xiaomusic/issues/210" target="_blank">文档</a></label>
      <input id="yt_dlp_cookies_file" name="file" type="file">
      <button id="upload_yt_dlp_cookie">上传</button>
    </div>
    <hr>

    <button onclick="location.href='/static/default/index.html';">返回首页</button>
    <button id="get_music_list">获取歌单</button>
    <button class="save-button">保存</button>
    <hr>

    <button id="refresh_music_tag">刷新tag</button>
    <button id="clear_cache">清空缓存</button>
    <a class="button" href="/downloadlog" download="xiaomusic.txt">下载日志文件</a>

    <hr>

    <button onclick="location.href='/docs';">查看接口文档</button>
    <a class="button" href="./m3u.html" target="_blank">m3u文件转换</a>
    <a class="button" href="./downloadtool.html" target="_blank">歌曲下载工具</a>
    <hr>

    <a class="button" href="./debug.html" target="_blank">调试工具</a>
    <a class="button" href="https://afdian.com/a/imhanxi" target="_blank">💰 爱发电</a>
    <a class="button" href="https://github.com/hanxi/xiaomusic" target="_blank">点个 Star ⭐</a>

    <div class="rows">
      <img class="qrcode" src="./qrcode.png" alt="请涵曦喝奶茶🧋">
    </div>
    <footer>
      <p>Powered by <a href="https://xdocs.hanxi.cc" target="_blank">XiaoMusic</a></p>
    </footer>
  </body>
</html>
