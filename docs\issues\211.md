---
title: 📝 文档汇总
---

# 📝 文档汇总

## 1️⃣ 基础文档

- [💬 FAQ问题集合](/issues/99.html)
- [如何修改默认的8090端口](/issues/19.html)
- [如何配置网络歌单](/issues/78.html)
- [如何添加m3u格式文件的电台](/issues/88.html)
- [xiaomusic极空间安装教程](/issues/297.html)
- [docker compose 命令行安装教程 ](/issues/360.html)

## 2️⃣ 进阶文档

- [设置项功能介绍](/issues/333.html)
- [采用config.json配置方式](/issues/94.html)
- [ios系统上的捷径配置](/issues/96.html)
- [【插件】自定义口令功能](/issues/105.html)
- [定时任务配置格式](/issues/182.html)
- [yt-dlp cookies 文件上传功能](/issues/210.html)
- [如何批量下载歌曲](/issues/212.html)
- [设备分组播放](/issues/65.html#issuecomment-2215736529)
- [如何播放小雅alist里的歌曲](/issues/128.html#issuecomment-2232867180)
- [如何添加 网易云音乐playlist](/issues/269.html)
- [相关工具推荐](/issues/285.html)

## 3️⃣ 其他安装文档

> [!NOTE]
> 下面教程可能比较旧，只供参考

- [群晖docker安装 xiaomusic](/issues/101.html)
- [NAS部署教程](https://post.m.smzdm.com/p/avpe7n99/)
- [群晖部署教程](https://post.m.smzdm.com/p/a7px7dol/)
- [QNAS部署教程](https://post.smzdm.com/p/a5xz5x63/)
- [视频教程-群晖1](https://www.bilibili.com/video/BV1ZZpweHEtT/)
- [视频教程-群晖2](https://www.bilibili.com/video/BV1JXxXeBEdY/)
- [视频教程-拾光坞N3](https://www.bilibili.com/video/BV1q629YMEG6/)
- [TechHive](https://mp.weixin.qq.com/s/4a41muFtPaFKtHeZYu795w)
- [弹个AI](https://mp.weixin.qq.com/s/sIsKxB7Y8b83AhnvaWiMog)
- [简单免费！教你用绿联NAS联动小爱音箱，私人音乐库也能语音点播](https://post.m.smzdm.com/p/a8pldgg7/)
- [飞牛教程](https://mp.weixin.qq.com/s?t=pages/image_detail&__biz=MzkxODc1NDMwOA==&mid=2247483725&idx=1&sn=2d615f14733b9bf989557fa766b4e1fc)


## 评论


### 评论 1 - sghuenn

redmi小爱触屏音箱8，仍然需要打开“型号兼容模式”才能播放
打开兼容模式后的问题有：每首歌曲播放完毕后都要再从头播放4、5秒才播放下一曲；语音命令小爱同学播放下一曲，它会从头开始播放当前歌曲。

---

### 评论 2 - hanxi

> redmi小爱触屏音箱8，仍然需要打开“型号兼容模式”才能播放 打开兼容模式后的问题有：每首歌曲播放完毕后都要再从头播放4、5秒才播放下一曲；语音命令小爱同学播放下一曲，它会从头开始播放当前歌曲。

播放歌曲的接口应该是有点问题，等有设备有开发能力的人来搞吧。

---

### 评论 3 - zhoukk37

想问下如何利用反向代理，完成使得小爱外网访问nas呢，您能提供一下关键词，我自己去检索学下一下吗

---

### 评论 4 - hanxi

> 想问下如何利用反向代理，完成使得小爱外网访问nas呢，您能提供一下关键词，我自己去检索学下一下吗

内网穿透，frp能实现，就是把局域网的端口映射成公网的端口。

---

### 评论 5 - Justlook99

按照飞牛的教程，部署成功了，一直没有设备显示出来，然后我也按照相应的问题集去处理：关闭本地代理。
如果是nas运行的，网络由bridge改为host。
米家app重新登陆。
mi.com官网重新登陆。
但是还是没有办法显示设备出来，请问到底是什么原因？最新的37版本。

---

### 评论 6 - hanxi

> 按照飞牛的教程，部署成功了，一直没有设备显示出来，然后我也按照相应的问题集去处理：关闭本地代理。 如果是nas运行的，网络由bridge改为host。 米家app重新登陆。 mi.com官网重新登陆。 但是还是没有办法显示设备出来，请问到底是什么原因？最新的37版本。

目前反馈的都是飞牛的用户，可能是飞牛有问题。

---

### 评论 7 - 3794313569

在同一个容器内，前后分别启动了mi-gpt和xiaomusic两个应用，现在通过日志发现，mi-gpt的日志一直在记录，语音需求基本都在mi-gpt这个应用响应了，请问下按照您现在设计的框架内，有没有办法可以实现这两个应用同时生效，或者稍后类似应用会有专用的通讯协议，保证多项应用在同一台机器上的响应。
类似：语音命令-“播放本地歌曲”触发xiaomusic，“召唤”（mi-gpt配置的唤醒词）触发mi-gpt，等等。。。。。。
暂时的办法就是买两个小爱音箱，不同的命名，然后一个应用配置一个did。

---

### 评论 8 - hanxi

> 在同一个容器内，前后分别启动了mi-gpt和xiaomusic两个应用，现在通过日志发现，mi-gpt的日志一直在记录，语音需求基本都在mi-gpt这个应用响应了，请问下按照您现在设计的框架内，有没有办法可以实现这两个应用同时生效，或者稍后类似应用会有专用的通讯协议，保证多项应用在同一台机器上的响应。 类似：语音命令-“播放本地歌曲”触发xiaomusic，“召唤”（mi-gpt配置的唤醒词）触发mi-gpt，等等。。。。。。 暂时的办法就是买两个小爱音箱，不同的命名，然后一个应用配置一个did。

可以分别部署到两个不同的容器里，两个应用的唤醒词是不同的，不会互相干扰。

---

### 评论 9 - Tranceboox

如果网页端主页内能显示播放曲目的封面就太牛了，我知道实现起来很难，就是臆想一下

---

### 评论 10 - hanxi

> 如果网页端主页内能显示播放曲目的封面就太牛了，我知道实现起来很难，就是臆想一下

xplayer 和 pure 主题就可以，你试试。

---

### 评论 11 - aries0311

Pure主题中，设备列表中只有本机，不显示小米音箱
![01](https://gproxy.hanxi.cc/proxy/user-attachments/assets/ca231b5f-ee4f-430b-a2b3-aca5aa395081)
![02](https://gproxy.hanxi.cc/proxy/user-attachments/assets/a3a83d1f-a822-42d0-bea9-b88bb1f5fd76)


---

### 评论 12 - fanyan1026

电视的小爱同学可以播放nas音乐吗

---

### 评论 13 - hanxi

> 电视的小爱同学可以播放nas音乐吗

应该是不行的

---

### 评论 14 - alililala

这个部署在了旁路由里，能否将下载目录设置成同网络的nas里呢

---

### 评论 15 - hanxi

> 这个部署在了旁路由里，能否将下载目录设置成同网络的nas里呢

配公网访问吧，局域网互通不会配的话很难教会的。

---

### 评论 16 - wjcroom

> 这个部署在了旁路由里，能否将下载目录设置成同网络的nas里呢

旁路由视同本地网络.  只要是同网段,如果有子路由.   小爱和主机尽量在主路由. 如果小爱在子路由,应该问题不大.但是 xiaomusic 和 nas,网络共享.必须在主路由.然后,xiaomusic所在地方,可以映射成本地目录.这也挺麻烦.  网络共享,和目录还是有差别的. 

NFS文件服务  , linux本地挂载远程NFS为目录.这个我曾搞过. 是不是有一定原因,nas不能跑docker呢.不是太耗费资源,普通nas都有的 吧.

---

### 评论 17 - rainman5170

要怎样屏蔽外网？我的nas有域名解析，发现加上后缀外网居然也能连上xiaomusic，或者加下登陆窗口也是好的，不然直接暴露在外网了

---

### 评论 18 - hanxi

> 要怎样屏蔽外网？我的nas有域名解析，发现加上后缀外网居然也能连上xiaomusic，或者加下登陆窗口也是好的，不然直接暴露在外网了

设置访问密码就行

---

### 评论 19 - rainman5170

> > 要怎样屏蔽外网？我的nas有域名解析，发现加上后缀外网居然也能连上xiaomusic，或者加下登陆窗口也是好的，不然直接暴露在外网了
> 
> 设置访问密码就行

要怎样设置？麻烦说一下在哪设置

---

### 评论 20 - hanxi

![Image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/6423fa6b-293a-45b6-9482-b1e42f94a31f)

@rainman5170 

---

### 评论 21 - rainman5170





> ![Image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/6423fa6b-293a-45b6-9482-b1e42f94a31f)
> 
> [@rainman5170](https://github.com/rainman5170)

好的，谢谢

---

### 评论 22 - xiayuxingtian

![Image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/6c0fbd9d-6f15-4f48-93cd-77a16ab36cdb)
安装好后无法识别本地文件夹歌曲，下载的歌曲也不知道在哪个文件夹

![Image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/96966106-6d43-4480-9d85-8ddef67f7267)
下载的这些歌曲也没找到在哪里，有没有大神帮忙解答一下

![Image](https://gproxy.hanxi.cc/proxy/user-attachments/assets/ccb86f62-4140-4a65-8242-69c41615965b)

---

### 评论 23 - hanxi

路径挂载错了，删掉最后一个试试。

---

### 评论 24 - wjjjw2150

之前在群辉docker上部署的xiaomusic一直使用正常，今天群辉开机掉了硬盘，恢复正常后容器就无法启动，重置后正常启动，但默认主题和Tailwind主题出现显示问题，刷新等按钮变成了英文字符显示，如图，另外两个主题没问题。清空浏览器缓存，换其他电脑和手机访问都是如此。然后删掉xiaomusic容器和镜像（0.37），重新拉了最新镜像（0.378）部署，问题依旧。虽然功能都正常但强迫症表示实在是难受。请教大神这是怎么回事呀？
抱歉不知道怎么插图，看这个链接
https://tutu.to/image/1.N0FHK
 https://tutu.to/image/N0Jwj

---

### 评论 25 - hanxi

估计是网络问题

---

### 评论 26 - s493321320

有办法添加到homeassistant里吗？

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/211)
