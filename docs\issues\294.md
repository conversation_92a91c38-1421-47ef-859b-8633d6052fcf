---
title: 关于M01型号的注意事项
---

# 关于M01型号的注意事项

M01:在0.3.55版本【型号兼容模式】与【特殊型号获取对话记录】都设为false或true，都可以语音了。
        如果【型号兼容模式】为 true，默认UI显示播放中，但音箱没声音。

型号:S12A、LX04、S12 在米家APP可以联动，比如客厅有人自定义指令:播放歌曲、关机...等
       而M01无论【型号兼容模式】与【特殊型号获取对话记录】设为false或true，都无法执行任何自定义指令…

![IMG_6460](https://gproxy.hanxi.cc/proxy/user-attachments/assets/0913e3fa-1f1a-47b0-b8b9-d308bd7793df)


## 评论


### 评论 1 - hanxi

M01 保持默认设置应该是能语音和播放的吧。自定义指令的功能应该是 M01 本身就不支持，可能属于放弃维护的产品吧。

---

### 评论 2 - bj803

> M01 保持默认设置应该是能语音和播放的吧。自定义指令的功能应该是 M01 本身就不支持，可能属于放弃维护的产品吧。

刚试了用:pause是可以暂停(不过只暂2分钟左右又自己播放了)

---

### 评论 3 - hanxi

只能网页里点关机按钮，或者语音关机。所有型号都一样。

---

### 评论 4 - bj803

> 型号:S12A、LX04、S12



> 只能网页里点关机按钮，或者语音关机。所有型号都一样。

在型号:S12A、LX04、S12 除了能网页里点关机按钮或者语音开关机外，能在米家APP自定义指令进行播放与关机(例如当客厅客有人自定义指令"播放歌曲"，无人自定义指令"关机"。M01自定义指令"pause"可以暂停，其他口令不行。

---

### 评论 5 - bj803

刚又试了一下，用自定义play stop、power off可以停止播放关机，用自定义play music可以播放音乐，可能M01不需要下划线

---

### 评论 6 - hanxi

感谢你的反馈。

---

### 评论 7 - CrazyJasonwell

LX04不能识别，为找到小爱音箱。。。

---

### 评论 8 - hanxi

> LX04不能识别，为找到小爱音箱。。。

找不到设备是登录问题。

---
[链接到 GitHub Issue](https://github.com/hanxi/xiaomusic/issues/294)
