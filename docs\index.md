---
# https://vitepress.dev/reference/default-theme-home-page
layout: home

hero:
  name: "XiaoMusic"
  text: "无限听歌<br>解放小爱音箱"
  tagline: 使用小爱音箱播放音乐，音乐使用 yt-dlp 下载
  actions:
    - theme: brand
      text: 快速开始
      link: /issues/index
    - theme: alt
      text: FAQ
      link: /issues/99
    - theme: alt
      text: GitHub
      link: https://github.com/hanxi/xiaomusic

features:
  - title: MIT 开源
    details: 完全开源，自主可控
  - title: 一键部署
    details: 支持 Docker 部署，兼容各大 NAS 平台
  - title: 口令自定义
    details: 可以完全自定义语音口令，可以写自己的插件
---

